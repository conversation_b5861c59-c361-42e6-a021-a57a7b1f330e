//
//  UIManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// UI管理器 - 负责游戏界面元素的创建和管理
class UIManager {

    // MARK: - Properties
    private weak var gameScene: GameScene?
    private var screenAdaptationManager: ScreenAdaptationManager?
    private var levelLabel: SKLabelNode?
    private var retryButton: SKLabelNode?
    private var undoButton: SKLabelNode?
    private var levelSelectButton: SKLabelNode?
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
        self.screenAdaptationManager = ScreenAdaptationManager(gameScene: gameScene)
    }
    
    // MARK: - Public Methods
    
    /// 设置所有UI元素
    func setupAllUIElements(safeAreaInsets: UIEdgeInsets, currentLevel: Int) {
        guard let scene = gameScene else { return }

        // 清理旧的UI元素
        cleanupOldUI(scene: scene)

        // 设置新的统一头部UI
        setupHeaderUI(scene: scene, safeAreaInsets: safeAreaInsets, currentLevel: currentLevel)
    }

    /// 清理旧的UI元素
    private func cleanupOldUI(scene: GameScene) {
        scene.children.forEach { node in
            if node.name?.contains("Button") == true ||
               node.name?.contains("Label") == true ||
               node.name?.contains("header") == true ||
               node.name?.contains("Background") == true {
                node.removeFromParent()
            }
        }
    }

    /// 设置统一的头部UI布局
    private func setupHeaderUI(scene: GameScene, safeAreaInsets: UIEdgeInsets, currentLevel: Int) {
        guard let adaptationManager = screenAdaptationManager else { return }

        let screenInfo = adaptationManager.getCurrentScreenInfo()
        let uiScale = screenInfo.scaleFactor

        // 计算头部区域 - 确保不占用游戏方格区域
        let headerHeight: CGFloat = 50 * uiScale
        let headerY = scene.size.height - safeAreaInsets.top - headerHeight/2 - 10 * uiScale // 额外向下偏移
        let margin: CGFloat = 15 * uiScale

        // 创建头部背景
        let headerBackground = SKShapeNode(rectOf: CGSize(width: scene.size.width, height: headerHeight))
        headerBackground.fillColor = SKColor.black.withAlphaComponent(0.15)
        headerBackground.strokeColor = SKColor.clear
        headerBackground.position = CGPoint(x: scene.size.width/2, y: headerY)
        headerBackground.zPosition = 98
        headerBackground.name = "headerBackground"
        scene.addChild(headerBackground)

        // 左侧：关卡信息
        setupLevelInfo(scene: scene, headerY: headerY, margin: margin, uiScale: uiScale, currentLevel: currentLevel)

        // 右侧：功能按钮组
        setupButtonGroup(scene: scene, headerY: headerY, margin: margin, uiScale: uiScale)
    }
    
    /// 更新关卡标签
    func updateLevelLabel(currentLevel: Int) {
        levelLabel?.text = "关卡 \(currentLevel + 1)"
    }
    
    /// 检查按钮点击
    func handleButtonTouch(at location: CGPoint) -> UIButtonType? {
        if let button = retryButton, button.contains(location) {
            return .retry
        }

        if let button = undoButton, button.contains(location) {
            return .undo
        }

        if let button = levelSelectButton, button.contains(location) {
            return .levelSelect
        }

        return nil
    }
    
    /// 播放按钮按压动画
    func animateButtonPress(buttonType: UIButtonType, completion: @escaping () -> Void) {
        let button: SKNode?

        switch buttonType {
        case .retry:
            button = retryButton
        case .undo:
            button = undoButton
        case .levelSelect:
            button = levelSelectButton
        }

        guard let targetButton = button else {
            completion()
            return
        }
        
        // 按压动画
        let scaleDown = SKAction.scale(to: 0.9, duration: 0.1)
        let scaleUp = SKAction.scale(to: 1.0, duration: 0.1)
        
        // 颜色闪烁效果
        let originalColor = (targetButton as? SKLabelNode)?.fontColor ?? SKColor.white
        let flashColor = SKColor.yellow
        let colorFlash = SKAction.colorize(with: flashColor, colorBlendFactor: 0.5, duration: 0.1)
        let colorRestore = SKAction.colorize(with: originalColor, colorBlendFactor: 1.0, duration: 0.1)
        
        // 组合动画
        let pressAnimation = SKAction.sequence([
            SKAction.group([scaleDown, colorFlash]),
            SKAction.group([scaleUp, colorRestore])
        ])
        
        targetButton.run(pressAnimation) {
            completion()
        }
    }
    
    // MARK: - Private Methods

    /// 设置左侧关卡信息
    private func setupLevelInfo(scene: GameScene, headerY: CGFloat, margin: CGFloat, uiScale: CGFloat, currentLevel: Int) {
        guard let adaptationManager = screenAdaptationManager else { return }

        // 关卡信息背景
        let infoWidth: CGFloat = 120 * uiScale
        let infoHeight: CGFloat = 40 * uiScale
        let infoBackground = SKShapeNode(rectOf: CGSize(width: infoWidth, height: infoHeight), cornerRadius: 20 * uiScale)
        infoBackground.fillColor = SKColor(red: 0.2, green: 0.4, blue: 0.8, alpha: 0.9)
        infoBackground.strokeColor = SKColor.white.withAlphaComponent(0.6)
        infoBackground.lineWidth = 2 * uiScale
        infoBackground.position = CGPoint(x: margin + infoWidth/2, y: headerY)
        infoBackground.zPosition = 99
        infoBackground.name = "levelInfoBackground"
        scene.addChild(infoBackground)

        // 关卡标签
        levelLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        levelLabel?.text = "关卡 \(currentLevel + 1)"
        levelLabel?.fontSize = adaptationManager.getAdaptedFontSize(baseFontSize: 18, fontScale: uiScale)
        levelLabel?.fontColor = SKColor.white
        levelLabel?.horizontalAlignmentMode = .center
        levelLabel?.verticalAlignmentMode = .center
        levelLabel?.position = infoBackground.position
        levelLabel?.zPosition = 100
        levelLabel?.name = "levelLabel"

        if let label = levelLabel {
            scene.addChild(label)
        }
    }

    /// 设置右侧按钮组
    private func setupButtonGroup(scene: GameScene, headerY: CGFloat, margin: CGFloat, uiScale: CGFloat) {
        guard let adaptationManager = screenAdaptationManager else { return }

        let buttonWidth: CGFloat = 60 * uiScale
        let buttonHeight: CGFloat = 35 * uiScale
        let buttonSpacing: CGFloat = 15 * uiScale

        // 计算按钮起始位置（从右往左排列）
        let startX = scene.size.width - margin - buttonWidth/2

        // 重试按钮（红色）
        setupCompactButton(
            scene: scene,
            position: CGPoint(x: startX, y: headerY),
            size: CGSize(width: buttonWidth, height: buttonHeight),
            color: SKColor(red: 0.9, green: 0.3, blue: 0.3, alpha: 0.9),
            text: "重试",
            name: "retryButton",
            uiScale: uiScale,
            buttonRef: &retryButton
        )

        // 回退按钮（绿色）
        setupCompactButton(
            scene: scene,
            position: CGPoint(x: startX - buttonWidth - buttonSpacing, y: headerY),
            size: CGSize(width: buttonWidth, height: buttonHeight),
            color: SKColor(red: 0.3, green: 0.7, blue: 0.3, alpha: 0.9),
            text: "回退",
            name: "undoButton",
            uiScale: uiScale,
            buttonRef: &undoButton
        )

        // 选关按钮（紫色）
        setupCompactButton(
            scene: scene,
            position: CGPoint(x: startX - 2 * (buttonWidth + buttonSpacing), y: headerY),
            size: CGSize(width: buttonWidth, height: buttonHeight),
            color: SKColor(red: 0.6, green: 0.3, blue: 0.8, alpha: 0.9),
            text: "选关",
            name: "levelSelectButton",
            uiScale: uiScale,
            buttonRef: &levelSelectButton
        )
    }

    /// 设置紧凑型按钮
    private func setupCompactButton(scene: GameScene, position: CGPoint, size: CGSize, color: SKColor, text: String, name: String, uiScale: CGFloat, buttonRef: inout SKLabelNode?) {
        guard let adaptationManager = screenAdaptationManager else { return }

        // 按钮背景
        let background = SKShapeNode(rectOf: size, cornerRadius: 17 * uiScale)
        background.fillColor = color
        background.strokeColor = SKColor.white.withAlphaComponent(0.7)
        background.lineWidth = 1.5 * uiScale
        background.position = position
        background.zPosition = 99
        background.name = "\(name)Background"
        scene.addChild(background)

        // 按钮文字
        buttonRef = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        buttonRef?.text = text
        buttonRef?.fontSize = adaptationManager.getAdaptedFontSize(baseFontSize: 14, fontScale: uiScale)
        buttonRef?.fontColor = SKColor.white
        buttonRef?.horizontalAlignmentMode = .center
        buttonRef?.verticalAlignmentMode = .center
        buttonRef?.position = position
        buttonRef?.zPosition = 100
        buttonRef?.name = name

        if let button = buttonRef {
            scene.addChild(button)
        }
    }




}

/// UI按钮类型枚举
enum UIButtonType {
    case retry      // 红色重置按钮
    case undo       // 绿色回退按钮
    case levelSelect // 紫色选关按钮
}
