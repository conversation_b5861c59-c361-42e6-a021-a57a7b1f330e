import SpriteKit
import GameplayKit
import AudioToolbox

class GameScene: SKScene {

    // MARK: - Properties
    private var lastUpdateTime : TimeInterval = 0

    // 管理器实例
    var gameStateManager: GameStateManager!
    private var deadlockDetector: DeadlockDetector!
    var uiManager: UIManager!
    var audioManager: AudioManager!
    private var initialTouch: CGPoint = .zero
    var isMoving: Bool = false // 防止连续快速滑动导致多次移动

    // 游戏元素节点
    var player: SKSpriteNode?
    var boxes: [SKSpriteNode] = []
    var walls: [SKSpriteNode] = []
    var targets: [SKSpriteNode] = []
    var groundTiles: [SKSpriteNode] = []

    var levelManager = LevelManager()
    var levelGenerator = LevelGenerator()
    var progressManager = LevelProgressManager()

    // 新的功能管理器
    private var touchManager: TouchManager!
    private var levelSelectionManager: LevelSelectionManager!
    var animationManager: AnimationManager!
    private var levelLoadingManager: LevelLoadingManager!
    var currentLevelIndex = 0
    private var levelLabel: SKLabelNode? // 用于显示当前关卡
    private var resetButton: SKLabelNode? // 重置按钮
    // Level selection removed for cleaner UI
    var levelData: [[Character]] = [] // 将由LevelManager提供

    // 游戏状态保存
    private var initialPlayerPosition: CGPoint = .zero
    private var initialBoxPositions: [CGPoint] = []
    private var moveHistory: [(playerPos: CGPoint, boxPositions: [CGPoint])] = []

    // 安全区域
    var safeAreaInsets: UIEdgeInsets = .zero

    // 瓦片大小 - 根据屏幕大小和关卡动态调整
    var tileSize: CGFloat = 64.0

    // 适配后的尺寸信息
    var adaptedSizes: ScreenAdaptationManager.AdaptedSizes?

    // 移动路径显示
    private var pathNodes: [SKSpriteNode] = []
    
    override init(size: CGSize) {
        super.init(size: size)
        print("[GameScene] Initialized with size: \(size)") // 调试信息：场景初始化大小

        // 根据屏幕大小动态调整瓦片大小
        let minDimension = min(size.width, size.height)
        tileSize = max(48.0, min(80.0, minDimension / 8.0))
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func sceneDidLoad() {
        // 初始化管理器
        gameStateManager = GameStateManager()
        deadlockDetector = DeadlockDetector(gameScene: self)
        uiManager = UIManager(gameScene: self)
        audioManager = AudioManager(gameScene: self)

        // 初始化新的功能管理器
        touchManager = TouchManager(gameScene: self)
        levelSelectionManager = LevelSelectionManager(gameScene: self)
        animationManager = AnimationManager(gameScene: self)
        levelLoadingManager = LevelLoadingManager(gameScene: self)

        // 设置渐变背景
        setupGradientBackground()
        print("[GameScene] sceneDidLoad called. Scene size: \(self.size)")
        self.lastUpdateTime = 0

        // 预加载音效
        audioManager.preloadSounds()

        // 运行LevelGenerator测试
        print("[GameScene] 🧪 开始测试LevelGenerator...")
        LevelGeneratorTest.runQuickTest()

        // 加载第一个关卡
        levelLoadingManager.loadLevel(index: currentLevelIndex)

        print("[GameScene] 游戏场景初始化完成")
        print(audioManager.getAudioStatusReport())
    }

    /// 设置统一的渐变背景
    func setupGradientBackground() {
        // 移除旧的背景
        self.children.filter { $0.name == "gameBackground" }.forEach { $0.removeFromParent() }

        // 创建真正的渐变背景
        let gradientTexture = createGradientTexture()
        let backgroundNode = SKSpriteNode(texture: gradientTexture)
        backgroundNode.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        backgroundNode.zPosition = -100
        backgroundNode.size = self.size
        backgroundNode.name = "gameBackground"
        self.addChild(backgroundNode)

        print("[GameScene] 统一渐变背景已设置")
    }

    /// 创建渐变纹理 - 根据README要求：深蓝色渐变，营造舒适游戏氛围
    private func createGradientTexture() -> SKTexture {
        let size = CGSize(width: 64, height: 64) // 增加纹理分辨率
        let renderer = UIGraphicsImageRenderer(size: size)

        let image = renderer.image { context in
            let cgContext = context.cgContext

            // 创建舒适的深蓝色渐变 - 更加柔和和舒适
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let colors = [
                UIColor(red: 0.05, green: 0.15, blue: 0.35, alpha: 1.0).cgColor,  // 深海蓝
                UIColor(red: 0.1, green: 0.25, blue: 0.45, alpha: 1.0).cgColor,   // 深蓝
                UIColor(red: 0.15, green: 0.35, blue: 0.55, alpha: 1.0).cgColor,  // 中深蓝
                UIColor(red: 0.2, green: 0.4, blue: 0.6, alpha: 1.0).cgColor     // 温和蓝
            ] as CFArray

            let gradient = CGGradient(colorsSpace: colorSpace, colors: colors, locations: [0.0, 0.3, 0.7, 1.0])!

            // 绘制从上到下的渐变，营造深度感
            cgContext.drawLinearGradient(
                gradient,
                start: CGPoint(x: 0, y: size.height),
                end: CGPoint(x: 0, y: 0),
                options: []
            )
        }

        return SKTexture(image: image)
    }
    
    /// 加载指定索引的关卡（公共接口）
    /// - Parameter index: 关卡索引
    func loadLevel(index: Int) {
        levelLoadingManager.loadLevel(index: index)
    }

    /// 加载指定索引的关卡（旧版本，保留用于兼容性）
    /// - Parameter index: 关卡索引
    private func loadLevelOld(index: Int) {
        print("[GameScene] Attempting to load level at index: \(index)") // 调试信息：尝试加载关卡

        // 根据用户要求：前5关是写死的教学关卡，其他关卡使用动态生成
        var newLevelData: [[Character]]?

        if index < 5 {
            // 前5关：使用写死的教学关卡
            newLevelData = levelManager.getLevel(at: index)
            print("[GameScene] Loading tutorial level from LevelManager: level \(index + 1)/5 (教学关卡)")
        } else {
            // 第6关及以后：使用动态生成的关卡
            let generatorIndex = index - 5 // 动态生成从第6关开始，对应generator的第0关
            if generatorIndex < levelGenerator.numberOfLevels {
                newLevelData = levelGenerator.getLevel(at: generatorIndex)
                print("[GameScene] Loading dynamic level from LevelGenerator: level \(index + 1) (动态生成第\(generatorIndex + 1)关)")
            } else {
                // 如果超出了动态生成的范围，循环使用动态关卡
                let cycleIndex = generatorIndex % levelGenerator.numberOfLevels
                newLevelData = levelGenerator.getLevel(at: cycleIndex)
                print("[GameScene] Loading cycled dynamic level: level \(index + 1) (循环第\(cycleIndex + 1)关)")
            }
        }

        if let levelData = newLevelData {
            self.levelData = levelData
            currentLevelIndex = index

            // 增加关卡尝试次数
            progressManager.incrementAttempts(levelIndex: index)

            // 根据关卡调整瓦片大小
            adjustTileSizeForLevel(index: index)

            print("[GameScene] Level data loaded successfully for index \(index)") // 调试信息：关卡数据加载成功

            // 使用LevelLoadingManager设置关卡，避免重复代码
            levelLoadingManager.setupLevel(with: levelData)

            // 保存初始状态到新的管理器
            saveInitialStateToManager()
        } else {
            print("无法加载关卡索引: \(index)。可能是最后一个关卡了。")
            // 可以选择显示游戏结束画面或循环到第一关
            // loadLevel(index: 0) // 回到第一关
            showGameCompletedScreen()
        }
    }

    /// 根据关卡调整瓦片大小
    /// - Parameter index: 关卡索引
    private func adjustTileSizeForLevel(index: Int) {
        let levelWidth = levelData.first?.count ?? 5
        let levelHeight = levelData.count

        // 计算合适的瓦片大小
        let availableWidth = self.size.width - 40 // 留出边距
        let availableHeight = self.size.height - safeAreaInsets.top - safeAreaInsets.bottom - 120 // 留出UI空间

        let maxTileWidth = availableWidth / CGFloat(levelWidth)
        let maxTileHeight = availableHeight / CGFloat(levelHeight)

        tileSize = min(maxTileWidth, maxTileHeight, 80) // 最大不超过80
        tileSize = max(tileSize, 40) // 最小不小于40

        print("[GameScene] Adjusted tile size to \(tileSize) for level \(index + 1)")
    }

    /// 保存初始状态到新的管理器
    private func saveInitialStateToManager() {
        // 确保玩家和箱子已经准备好
        guard let playerNode = self.player, !boxes.isEmpty else {
            print("[GameScene] ERROR: Cannot save initial state - player or boxes not ready")
            return
        }

        let playerGridPos = gridPosition(for: playerNode.position)
        let boxGridPositions = boxes.map { self.gridPosition(for: $0.position) }

        gameStateManager.saveInitialState(playerPosition: playerGridPos, boxPositions: boxGridPositions)

        print("[GameScene] Initial state saved to manager - Player: \(playerGridPos), Boxes: \(boxGridPositions)")
        print("[GameScene] Number of boxes saved: \(boxGridPositions.count)")
    }

    /// 保存初始状态（旧版本，保留用于兼容性）
    private func saveInitialState() {
        // CRITICAL FIX: Ensure we have valid player and boxes before saving
        guard let playerNode = self.player, !boxes.isEmpty else {
            print("[GameScene] ERROR: Cannot save initial state - player or boxes not ready")
            return
        }

        initialPlayerPosition = playerNode.position
        initialBoxPositions = boxes.map { $0.position }

        print("[GameScene] Initial state saved - Player: \(initialPlayerPosition), Boxes: \(initialBoxPositions)")
        print("[GameScene] Number of boxes saved: \(initialBoxPositions.count)")
    }

    /// 显示游戏通关画面（占位）
    func showGameCompletedScreen() {
        let completedLabel = SKLabelNode(fontNamed: "Chalkduster")
        completedLabel.text = "所有关卡完成!"
        completedLabel.fontSize = 40
        completedLabel.fontColor = SKColor.blue
        completedLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        completedLabel.zPosition = 100
        self.addChild(completedLabel)
        // 可以在这里添加返回主菜单或重置游戏的按钮
    }
    
    /// 设置关卡
    /// 根据 levelData 初始化游戏场景中的各个元素
    func setupLevel() {
        print("[GameScene] Setting up level with current data...") // 调试信息：开始设置关卡
        // 清理旧的节点（如果有关卡重载功能）
        self.removeAllChildren() // 清除所有旧节点，包括UI元素
        boxes.removeAll()
        walls.removeAll()
        targets.removeAll()
        groundTiles.removeAll()
        player = nil
        levelLabel = nil // 清除旧的标签引用
        resetButton = nil // 清除旧的按钮引用

        // 重新设置统一的渐变背景
        setupGradientBackground()

        // 使用UIManager设置所有UI元素
        uiManager.setupAllUIElements(safeAreaInsets: safeAreaInsets, currentLevel: currentLevelIndex)

        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0

        // 计算场景的中心偏移量，使关卡居中
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize
        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0
        print("[GameScene] Level dimensions: \(levelWidth)x\(levelHeight), TileSize: \(tileSize)") // 调试信息：关卡维度和瓦片大小
        print("[GameScene] Calculated scene size: \(sceneWidth)x\(sceneHeight), Offset: (\(offsetX), \(offsetY))") // 调试信息：计算出的场景大小和偏移

        for r in 0..<levelHeight {
            for c in 0..<levelWidth {
                let char = levelData[r][c]
                let position = CGPoint(x: CGFloat(c) * tileSize + tileSize / 2.0 + offsetX,
                                       y: self.size.height - (CGFloat(r) * tileSize + tileSize / 2.0) - offsetY) // Y轴反转，因为SpriteKit原点在左下角
                
                // 始终先添加地面 - 使用更柔和的颜色
                let groundNode = createGroundTile()
                groundNode.position = position
                groundNode.zPosition = 0 // 地面在最底层
                self.addChild(groundNode)
                groundTiles.append(groundNode)

                switch char {
                case "W": // 墙
                    let wallNode = createWallTile()
                    wallNode.position = position
                    self.addChild(wallNode)
                    walls.append(wallNode)
                case "P": // 玩家
                    let playerNode = createPlayerTile()
                    playerNode.position = position
                    self.addChild(playerNode)
                    self.player = playerNode
                case "B": // 箱子
                    let boxNode = createBoxTile()
                    boxNode.position = position
                    self.addChild(boxNode)
                    boxes.append(boxNode)
                case "T": // 目标点
                    let targetNode = createTargetTile()
                    targetNode.position = position
                    self.addChild(targetNode)
                    targets.append(targetNode)
                case "G": // 空地 (已经由默认地面处理)
                    break
                default:
                    print("未知的关卡字符: \(char)")
                }
            }
        }
        print("[GameScene] setupLevel completed. Scene size: \(self.size), Children count: \(self.children.count)") // 调试信息：setupLevel完成后的场景大小和子节点数量
    }

    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchManager.handleTouchesBegan(touches, with: event)
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        // 点击控制不需要处理移动事件
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        // 点击控制不需要处理结束事件
    }
    
    /// 移动玩家 - 标准推箱子移动，确保严格网格对齐
    /// - Parameter direction: 移动方向 (只允许上下左右四个方向: up=0,1 down=0,-1 left=-1,0 right=1,0)
    func movePlayer(direction: CGVector) {
        guard let playerNode = self.player, !isMoving else {
            print("[GameScene] 移动被阻止: 玩家节点不存在或正在移动中")
            return
        }

        // 验证方向是否为标准四方向之一
        let validDirections: [CGVector] = [
            CGVector(dx: 0, dy: 1),   // 上
            CGVector(dx: 0, dy: -1),  // 下
            CGVector(dx: -1, dy: 0),  // 左
            CGVector(dx: 1, dy: 0)    // 右
        ]

        guard validDirections.contains(where: { $0.dx == direction.dx && $0.dy == direction.dy }) else {
            print("[GameScene] 无效的移动方向: (\(direction.dx), \(direction.dy))，只允许上下左右移动")
            return
        }

        isMoving = true // 开始移动
        print("[GameScene] 开始移动: 方向(\(direction.dx), \(direction.dy))")

        let currentGridPosition = gridPosition(for: playerNode.position)
        let targetGridPosition = CGPoint(x: currentGridPosition.x + direction.dx,
                                         y: currentGridPosition.y + direction.dy)

        // 保存移动前的状态到历史记录 - 用于回退功能
        let currentBoxPositions = boxes.map { gridPosition(for: $0.position) }
        gameStateManager.saveMoveState(playerPosition: currentGridPosition, boxPositions: currentBoxPositions)

        // 检查目标位置是否有效（边界检查）
        if !isValidGridPosition(targetGridPosition) {
            print("[GameScene] 移动超出边界! 目标位置: \(targetGridPosition)")
            isMoving = false
            audioManager.playSound(.error)
            return
        }

        // 检查目标位置是否为墙
        if isWall(at: targetGridPosition) {
            print("[GameScene] 撞墙了! 目标位置: \(targetGridPosition)")
            isMoving = false // 撞墙，移动结束
            audioManager.playSound(.error)
            return
        }

        // 根据标准推箱子规则，玩家可以移动到目标位置
        // 移除了原来的目标位置限制，因为这不符合标准推箱子游戏规则

        // 检查目标位置是否有箱子 - 推箱子逻辑
        if let boxToPush = box(at: targetGridPosition) {
            let nextGridPositionForBox = CGPoint(x: targetGridPosition.x + direction.dx,
                                                 y: targetGridPosition.y + direction.dy)

            // 推箱子规则检查：
            // 1. 箱子前方不能是墙
            // 2. 箱子前方不能有其他箱子（一次只能推一个箱子）
            // 3. 箱子前方必须在游戏区域内
            if isWall(at: nextGridPositionForBox) ||
               box(at: nextGridPositionForBox) != nil ||
               !isValidGridPosition(nextGridPositionForBox) {
                print("[GameScene] 箱子推不动! 箱子目标位置: \(nextGridPositionForBox)")
                print("[GameScene] 原因: 墙=\(isWall(at: nextGridPositionForBox)), 其他箱子=\(box(at: nextGridPositionForBox) != nil), 越界=\(!isValidGridPosition(nextGridPositionForBox))")
                isMoving = false
                audioManager.playSound(.error)
                return
            }

            print("[GameScene] 推箱子: 箱子从 \(targetGridPosition) 移动到 \(nextGridPositionForBox)")

            // 同时移动玩家和箱子，确保位置同步
            let playerTargetScenePosition = scenePosition(for: targetGridPosition)
            let boxTargetScenePosition = scenePosition(for: nextGridPositionForBox)

            // 玩家移动动画
            let movePlayerAction = SKAction.move(to: playerTargetScenePosition, duration: 0.15)
            movePlayerAction.timingMode = .easeInEaseOut

            // 箱子移动动画
            let moveBoxAction = SKAction.move(to: boxTargetScenePosition, duration: 0.15)
            moveBoxAction.timingMode = .easeInEaseOut

            // 给箱子移动添加视觉反馈
            let scaleUp = SKAction.scale(to: 1.05, duration: 0.075)
            let scaleDown = SKAction.scale(to: 1.0, duration: 0.075)
            let scaleSequence = SKAction.sequence([scaleUp, scaleDown])
            let boxGroupAction = SKAction.group([moveBoxAction, scaleSequence])

            // 同时执行玩家和箱子的移动
            playerNode.run(movePlayerAction)
            boxToPush.run(boxGroupAction) {
                // 确保最终位置精确对齐
                playerNode.position = self.scenePosition(for: targetGridPosition)
                boxToPush.position = self.scenePosition(for: nextGridPositionForBox)

                // 播放推箱子音效
                self.audioManager.playSound(.push)

                // 移动完成
                self.isMoving = false
                print("[GameScene] 推箱子完成: 玩家位置 \(targetGridPosition), 箱子位置 \(nextGridPositionForBox)")

                // 推箱子后检查死锁状态
                if self.deadlockDetector.checkForDeadlock(boxes: self.boxes, targets: self.targets) {
                    print("[GameScene] 🚨 检测到死锁状态!")
                    self.showDeadlockAlert()
                }

                // 检查胜利条件
                self.checkWinCondition()
            }
            return // 推箱子完成，直接返回
        }

        // 移动玩家 - 确保像素完美对齐
        let targetScenePosition = scenePosition(for: targetGridPosition)
        let movePlayerAction = SKAction.move(to: targetScenePosition, duration: 0.15)
        movePlayerAction.timingMode = .easeInEaseOut

        // 给玩家移动添加轻微的视觉反馈
        let wiggleRight = SKAction.rotate(byAngle: .pi / 32, duration: 0.04)
        let wiggleLeft = SKAction.rotate(byAngle: -.pi / 32, duration: 0.04)
        let straighten = SKAction.rotate(toAngle: 0, duration: 0.04)
        let wiggleSequence = SKAction.sequence([wiggleRight, wiggleLeft, straighten])

        let playerGroupAction = SKAction.group([movePlayerAction, wiggleSequence])

        playerNode.run(playerGroupAction) {
            // 确保玩家位置精确对齐
            playerNode.position = self.scenePosition(for: targetGridPosition)

            // 移动完成
            self.isMoving = false
            print("[GameScene] 移动完成: 新位置 \(targetGridPosition)")

            // 播放移动音效
            self.audioManager.playSound(.buttonPress)

            // 检查死锁状态 - 根据README，每走一步都要去检查是否处于无解状态
            if self.deadlockDetector.checkForDeadlock(boxes: self.boxes, targets: self.targets) {
                print("[GameScene] 🚨 检测到死锁状态!")
                self.showDeadlockAlert()
            }

            // 检查胜利条件
            self.checkWinCondition()
        }
    }

    // 移动历史保存函数已移至GameStateManager



    // 死锁检测函数已移至DeadlockDetector

    /// 显示死锁警告对话框
    private func showDeadlockAlert() {
        // 播放失败音效序列
        audioManager.playFailureSequence()

        // 创建半透明背景
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.8), size: self.size)
        overlay.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        overlay.zPosition = 300
        overlay.name = "deadlockOverlay"
        self.addChild(overlay)

        // 创建警告框背景
        let alertBackground = SKShapeNode(rectOf: CGSize(width: 280, height: 180), cornerRadius: 15)
        alertBackground.fillColor = SKColor.white
        alertBackground.strokeColor = SKColor.red
        alertBackground.lineWidth = 3
        alertBackground.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        alertBackground.zPosition = 301
        alertBackground.name = "deadlockAlert"
        self.addChild(alertBackground)

        // 标题
        let titleLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        titleLabel.text = "死锁检测"
        titleLabel.fontSize = 22
        titleLabel.fontColor = SKColor.red
        titleLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 40)
        titleLabel.zPosition = 302
        titleLabel.name = "deadlockTitle"
        self.addChild(titleLabel)

        // 消息
        let messageLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        messageLabel.text = "箱子被推到无法移动的位置！"
        messageLabel.fontSize = 16
        messageLabel.fontColor = SKColor.black
        messageLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 5)
        messageLabel.zPosition = 302
        messageLabel.name = "deadlockMessage"
        self.addChild(messageLabel)

        // 重试按钮 - 居中放置
        let retryButton = SKShapeNode(rectOf: CGSize(width: 120, height: 40), cornerRadius: 10)
        retryButton.fillColor = SKColor.orange
        retryButton.strokeColor = SKColor.black
        retryButton.lineWidth = 2
        retryButton.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 50)
        retryButton.zPosition = 302
        retryButton.name = "deadlockRetry"
        self.addChild(retryButton)

        let retryLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        retryLabel.text = "重试关卡"
        retryLabel.fontSize = 18
        retryLabel.fontColor = SKColor.white
        retryLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 58)
        retryLabel.zPosition = 303
        retryLabel.name = "deadlockRetryLabel"
        self.addChild(retryLabel)


    }

    /// 关闭死锁警告对话框
    func closeDeadlockAlert() {
        self.children.filter { node in
            node.name?.contains("deadlock") == true
        }.forEach { $0.removeFromParent() }
    }

    /// 精确网格位置计算：将场景坐标转换为网格坐标
    /// - Parameter scenePosition: SpriteKit场景坐标
    /// - Returns: 对应的网格坐标 (列, 行)
    func gridPosition(for scenePosition: CGPoint) -> CGPoint {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0

        // 计算关卡在屏幕上的起始位置
        let totalLevelWidth = CGFloat(levelWidth) * tileSize
        let totalLevelHeight = CGFloat(levelHeight) * tileSize
        let startX = (self.size.width - totalLevelWidth) / 2 + tileSize / 2
        let startY = (self.size.height - totalLevelHeight) / 2 + tileSize / 2 + 60 // 向上偏移给UI留空间

        // 精确计算网格位置，确保严格对齐
        let col = round((scenePosition.x - startX) / tileSize)
        let row = round((startY + CGFloat(levelHeight - 1) * tileSize - scenePosition.y) / tileSize)

        // 确保网格坐标在有效范围内
        let clampedCol = max(0, min(CGFloat(levelWidth - 1), col))
        let clampedRow = max(0, min(CGFloat(levelHeight - 1), row))

        return CGPoint(x: clampedCol, y: clampedRow)
    }

    /// 精确网格坐标转场景坐标：将网格坐标转换为场景坐标
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 对应的SpriteKit场景坐标
    func scenePosition(for gridPosition: CGPoint) -> CGPoint {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0

        // 计算关卡在屏幕上的起始位置（与gridPosition方法保持一致）
        let totalLevelWidth = CGFloat(levelWidth) * tileSize
        let totalLevelHeight = CGFloat(levelHeight) * tileSize
        let startX = (self.size.width - totalLevelWidth) / 2 + tileSize / 2
        let startY = (self.size.height - totalLevelHeight) / 2 + tileSize / 2 + 60 // 向上偏移给UI留空间

        // 精确计算场景位置，确保与gridPosition方法的逆运算一致
        let x = round(startX + CGFloat(gridPosition.x) * tileSize)
        let y = round(startY + CGFloat(levelHeight - 1) * tileSize - CGFloat(gridPosition.y) * tileSize)

        return CGPoint(x: x, y: y)
    }

    /// 检查指定网格位置是否是墙
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是墙则返回true，否则返回false
    /// 根据网格坐标获取箱子节点
    /// - Parameter gridPosition: 网格坐标
    /// - Returns: 如果该位置有箱子，则返回对应的SKSpriteNode，否则返回nil
    func box(at gridPosition: CGPoint) -> SKSpriteNode? {
        for boxNode in boxes {
            if self.gridPosition(for: boxNode.position) == gridPosition {
                return boxNode
            }
        }
        return nil
    }

    /// 检查指定网格位置是否是墙
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是墙则返回true，否则返回false
    func isWall(at gridPosition: CGPoint) -> Bool {
        let r = Int(gridPosition.y)
        let c = Int(gridPosition.x)

        // 边界检查
        guard r >= 0 && r < levelData.count && c >= 0 && c < (levelData.first?.count ?? 0) else {
            return true // 越界视为墙
        }
        return levelData[r][c] == "W"
    }

    /// 检查指定网格位置是否是目标位置（用于死锁检测）
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果是目标位置则返回true，否则返回false
    func isTargetPosition(at gridPosition: CGPoint) -> Bool {
        // 检查是否有目标节点在这个位置
        for targetNode in targets {
            let targetGridPos = self.gridPosition(for: targetNode.position)
            if targetGridPos == gridPosition {
                return true
            }
        }
        return false
    }

    /// 检查指定网格位置是否为目标点 (用于玩家移动限制)
    func isTarget(at gridPosition: CGPoint) -> Bool {
        return isTargetPosition(at: gridPosition)
    }

    /// 检查网格位置是否在有效范围内
    /// - Parameter gridPosition: 网格坐标 (列, 行)
    /// - Returns: 如果位置有效则返回true，否则返回false
    func isValidGridPosition(_ gridPosition: CGPoint) -> Bool {
        let r = Int(gridPosition.y)
        let c = Int(gridPosition.x)

        // 边界检查
        return r >= 0 && r < levelData.count && c >= 0 && c < (levelData.first?.count ?? 0)
    }
    

    

    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        // 触摸取消逻辑
    }
    
    
    override func update(_ currentTime: TimeInterval) {
        // Called before each frame is rendered
        
        // Initialize _lastUpdateTime if it has not been set yet
        if (self.lastUpdateTime == 0) {
            self.lastUpdateTime = currentTime
        }
        
        // Calculate time since last update
        let _ = currentTime - self.lastUpdateTime
        
        // 在这里更新游戏状态
        
        self.lastUpdateTime = currentTime
    }
    
    /// 检查胜利条件：所有箱子是否都在目标点上
    func checkWinCondition() {
        var allBoxesOnTargets = true
        if targets.isEmpty { // 如果没有目标点，则不判断胜利 (例如某些特殊关卡)
            return
        }
        if boxes.count != targets.count { // 箱子和目标点数量不一致，肯定无法胜利
            // 这个判断可能需要根据实际游戏逻辑调整，例如允许更多目标点
            // allBoxesOnTargets = false
            print("警告: 箱子数量 (\(boxes.count)) 与目标点数量 (\(targets.count)) 不匹配。")
            // return // 如果严格要求数量匹配，则直接返回
        }

        for boxNode in boxes {
            let boxGridPos = gridPosition(for: boxNode.position)
            var isOnTarget = false
            for targetNode in targets {
                if gridPosition(for: targetNode.position) == boxGridPos {
                    isOnTarget = true
                    // 可以给箱子或目标点一个视觉反馈，比如变色
                    // boxNode.color = .yellow // 示例：箱子在目标点上变黄
                    break
                }
            }
            if !isOnTarget {
                allBoxesOnTargets = false
                break
            }
        }
        
        if allBoxesOnTargets {
            print("恭喜！通过本关！")

            // 保存关卡完成进度
            let moveCount = gameStateManager.getCurrentMoveCount()
            progressManager.markLevelCompleted(levelIndex: currentLevelIndex, moveCount: moveCount)

            // 播放通关动画和音效
            playWinAnimationAndSound()

            // 延迟一段时间后加载下一关或返回菜单
            let waitAction = SKAction.wait(forDuration: 2.0) // 等待2秒
            let nextLevelAction = SKAction.run { [weak self] in
                guard let self = self else { return }
                self.loadLevel(index: self.currentLevelIndex + 1)
            }
            self.run(SKAction.sequence([waitAction, nextLevelAction]))
        }
    }
    
    /// VICTORY ANIMATION ENHANCEMENT: Professional victory animation with multiple effects
    func playWinAnimationAndSound() {
        // Create semi-transparent overlay
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.6), size: self.size)
        overlay.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        overlay.zPosition = 150
        overlay.name = "victoryOverlay"
        self.addChild(overlay)

        // Create victory background panel
        let victoryPanel = SKShapeNode(rectOf: CGSize(width: 300, height: 200), cornerRadius: 20)
        victoryPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        victoryPanel.strokeColor = SKColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0) // Gold color
        victoryPanel.lineWidth = 4
        victoryPanel.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        victoryPanel.zPosition = 151
        victoryPanel.name = "victoryPanel"
        self.addChild(victoryPanel)

        // Main victory label with enhanced styling
        let winLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        winLabel.text = "🎉 胜利! 🎉"
        winLabel.fontSize = 36
        winLabel.fontColor = SKColor(red: 0.0, green: 0.7, blue: 0.0, alpha: 1.0)
        winLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 30)
        winLabel.zPosition = 152
        winLabel.name = "victoryLabel"
        self.addChild(winLabel)

        // Level completion message
        let levelLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        levelLabel.text = "关卡 \(currentLevelIndex + 1) 完成!"
        levelLabel.fontSize = 20
        levelLabel.fontColor = SKColor.darkGray
        levelLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 10)
        levelLabel.zPosition = 152
        levelLabel.name = "levelCompleteLabel"
        self.addChild(levelLabel)

        // Next level preview - 支持200关
        let nextLevelLabel = SKLabelNode(fontNamed: "HelveticaNeue")
        let totalLevels = levelGenerator.numberOfLevels
        if currentLevelIndex + 1 < totalLevels {
            nextLevelLabel.text = "准备进入关卡 \(currentLevelIndex + 2)/\(totalLevels)..."
        } else {
            nextLevelLabel.text = "恭喜完成所有\(totalLevels)关!"
        }
        nextLevelLabel.fontSize = 16
        nextLevelLabel.fontColor = SKColor.blue
        nextLevelLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 40)
        nextLevelLabel.zPosition = 152
        nextLevelLabel.name = "nextLevelLabel"
        self.addChild(nextLevelLabel)

        // Enhanced animations
        // Panel entrance animation
        victoryPanel.setScale(0)
        let panelScaleUp = SKAction.scale(to: 1.0, duration: 0.4)
        panelScaleUp.timingMode = .easeOut
        victoryPanel.run(panelScaleUp)

        // Label entrance animation with bounce
        winLabel.setScale(0)
        let labelScaleUp = SKAction.scale(to: 1.2, duration: 0.3)
        let labelScaleDown = SKAction.scale(to: 1.0, duration: 0.2)
        labelScaleUp.timingMode = .easeOut
        labelScaleDown.timingMode = .easeIn
        let bounceSequence = SKAction.sequence([labelScaleUp, labelScaleDown])
        winLabel.run(SKAction.sequence([SKAction.wait(forDuration: 0.2), bounceSequence]))

        // Color cycling animation for victory label
        let colorCycle = SKAction.sequence([
            SKAction.colorize(with: SKColor.green, colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor(red: 1.0, green: 0.84, blue: 0.0, alpha: 1.0), colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor.blue, colorBlendFactor: 1.0, duration: 0.5),
            SKAction.colorize(with: SKColor.green, colorBlendFactor: 1.0, duration: 0.5)
        ])
        winLabel.run(SKAction.repeatForever(colorCycle))

        // Sparkle effects around the victory panel
        createSparkleEffects(around: victoryPanel.position)

        // Play enhanced victory sound sequence
        audioManager.playVictorySequence()

        // Auto-remove after delay
        let waitAction = SKAction.wait(forDuration: 3.0)
        let fadeOut = SKAction.fadeOut(withDuration: 0.5)
        let removeAction = SKAction.removeFromParent()
        let cleanupAction = SKAction.run { [weak self] in
            self?.removeVictoryElements()
        }

        overlay.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        victoryPanel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        winLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        levelLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction]))
        nextLevelLabel.run(SKAction.sequence([waitAction, fadeOut, removeAction, cleanupAction]))
    }

    /// Create sparkle effects around victory panel
    private func createSparkleEffects(around position: CGPoint) {
        for i in 0..<12 {
            let angle = CGFloat(i) * .pi / 6 // 30 degrees apart
            let radius: CGFloat = 180
            let sparkleX = position.x + cos(angle) * radius
            let sparkleY = position.y + sin(angle) * radius

            let sparkle = SKShapeNode(circleOfRadius: 4)
            sparkle.fillColor = SKColor.yellow
            sparkle.strokeColor = SKColor.orange
            sparkle.lineWidth = 1
            sparkle.position = CGPoint(x: sparkleX, y: sparkleY)
            sparkle.zPosition = 153
            sparkle.name = "victorySparkle"
            self.addChild(sparkle)

            // Sparkle animation
            let fadeIn = SKAction.fadeIn(withDuration: 0.2)
            let scale = SKAction.scale(to: 1.5, duration: 0.3)
            let fadeOut = SKAction.fadeOut(withDuration: 0.5)
            let remove = SKAction.removeFromParent()

            let sparkleSequence = SKAction.sequence([
                SKAction.wait(forDuration: Double(i) * 0.1), // Stagger the sparkles
                SKAction.group([fadeIn, scale]),
                SKAction.wait(forDuration: 2.0),
                fadeOut,
                remove
            ])

            sparkle.run(sparkleSequence)
        }
    }

    /// Remove all victory-related elements
    private func removeVictoryElements() {
        self.children.filter { node in
            node.name?.contains("victory") == true
        }.forEach { $0.removeFromParent() }
    }

    
    /// 重置当前关卡 - 改进版本，确保完全重置
    func resetLevel() {
        print("[GameScene] 🔄 重置关卡: \(currentLevelIndex + 1)")

        // 停止所有正在进行的动作
        isMoving = false
        self.removeAllActions()

        // 停止所有节点的动作
        player?.removeAllActions()
        for box in boxes {
            box.removeAllActions()
        }

        // 关闭死锁警告（如果存在）
        closeDeadlockAlert()

        guard let initialState = gameStateManager.resetToInitialState() else {
            print("[GameScene] ERROR: Cannot reset - no initial state saved")
            audioManager.playSound(.error)
            return
        }

        guard let playerNode = self.player else {
            print("[GameScene] ERROR: Cannot reset - player node not found")
            return
        }

        guard boxes.count == initialState.boxPositions.count else {
            print("[GameScene] ERROR: Box count mismatch - current: \(boxes.count), initial: \(initialState.boxPositions.count)")
            return
        }

        // 重置玩家位置 - 确保像素完美对齐
        let playerScenePosition = scenePosition(for: initialState.playerPosition)
        playerNode.position = playerScenePosition
        playerNode.removeAllActions() // 清除所有动作
        playerNode.zRotation = 0 // 重置旋转
        print("[GameScene] Player reset to: \(initialState.playerPosition) -> \(playerScenePosition)")

        // 重置箱子位置 - 确保像素完美对齐
        for (index, boxNode) in boxes.enumerated() {
            if index < initialState.boxPositions.count {
                let boxScenePosition = scenePosition(for: initialState.boxPositions[index])
                boxNode.position = boxScenePosition
                boxNode.removeAllActions() // 清除所有动作
                boxNode.setScale(1.0) // 重置缩放
                boxNode.zRotation = 0 // 重置旋转
                print("[GameScene] Box \(index) reset to: \(initialState.boxPositions[index]) -> \(boxScenePosition)")
            }
        }

        // 清除移动路径显示
        clearMovementPath()

        // 重置移动状态
        isMoving = false

        // 播放重置音效
        audioManager.playSound(.reset)

        print("[GameScene] ✅ 关卡已完全重置到初始状态")
    }

    /// 回退到上一步操作 - 根据README实现回退功能
    func undoLastMove() {
        print("[GameScene] 🔙 尝试回退上一步操作")

        // 停止当前移动
        isMoving = false
        self.removeAllActions()

        // 停止所有节点的动作
        player?.removeAllActions()
        for box in boxes {
            box.removeAllActions()
        }

        // 关闭死锁警告（如果存在）
        closeDeadlockAlert()

        guard let previousState = gameStateManager.undoLastMove() else {
            print("[GameScene] 没有可回退的移动")
            audioManager.playSound(.error)
            return
        }

        guard let playerNode = self.player else {
            print("[GameScene] ERROR: Cannot undo - player node not found")
            return
        }

        guard boxes.count == previousState.boxPositions.count else {
            print("[GameScene] ERROR: Box count mismatch during undo - current: \(boxes.count), previous: \(previousState.boxPositions.count)")
            return
        }

        // 回退玩家位置 - 确保像素完美对齐
        let playerScenePosition = scenePosition(for: previousState.playerPosition)
        playerNode.position = playerScenePosition
        playerNode.removeAllActions()
        playerNode.zRotation = 0
        print("[GameScene] Player undone to: \(previousState.playerPosition) -> \(playerScenePosition)")

        // 回退箱子位置 - 确保像素完美对齐
        for (index, boxNode) in boxes.enumerated() {
            if index < previousState.boxPositions.count {
                let boxScenePosition = scenePosition(for: previousState.boxPositions[index])
                boxNode.position = boxScenePosition
                boxNode.removeAllActions()
                boxNode.setScale(1.0)
                boxNode.zRotation = 0
                print("[GameScene] Box \(index) undone to: \(previousState.boxPositions[index]) -> \(boxScenePosition)")
            }
        }

        // 清除移动路径显示
        clearMovementPath()

        // 播放回退音效
        audioManager.playSound(.undo)

        print("[GameScene] ✅ 已回退到上一步状态")
    }

    /// 显示关卡选择菜单 - 根据README实现关卡选择功能
    func showLevelSelectionMenu() {
        levelSelectionManager.showLevelSelectionMenu()
    }

    /// 关闭关卡选择菜单
    func closeLevelSelectionMenu() {
        levelSelectionManager.closeLevelSelectionMenu()
    }

    /// 显示关卡选择菜单（旧版本，保留用于兼容性）
    private func showLevelSelectionMenuOld() {
        print("[GameScene] 显示关卡选择菜单")

        // 创建半透明背景遮罩
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.7), size: self.size)
        overlay.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        overlay.zPosition = 200
        overlay.name = "levelSelectOverlay"
        self.addChild(overlay)

        // 创建菜单背景面板
        let menuPanel = SKShapeNode(rectOf: CGSize(width: 400, height: 500), cornerRadius: 20)
        menuPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        menuPanel.strokeColor = SKColor.systemPurple
        menuPanel.lineWidth = 4
        menuPanel.position = CGPoint(x: self.size.width/2, y: self.size.height/2)
        menuPanel.zPosition = 201
        menuPanel.name = "levelSelectPanel"
        self.addChild(menuPanel)

        // 标题
        let titleLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        titleLabel.text = "选择关卡"
        titleLabel.fontSize = 28
        titleLabel.fontColor = SKColor.systemPurple
        titleLabel.position = CGPoint(x: self.size.width/2, y: self.size.height/2 + 200)
        titleLabel.zPosition = 202
        titleLabel.name = "levelSelectTitle"
        self.addChild(titleLabel)

        // 获取总关卡数 - 支持教学关卡+动态生成关卡
        let tutorialLevels = 5
        let dynamicLevels = min(levelGenerator.numberOfLevels, 45) // 显示45个动态关卡
        let totalLevels = tutorialLevels + dynamicLevels
        let levelsPerRow = 5
        let buttonSize: CGFloat = 60
        let spacing: CGFloat = 80

        // 创建关卡按钮网格
        for i in 0..<totalLevels {
            let row = i / levelsPerRow
            let col = i % levelsPerRow

            let buttonX = self.size.width/2 - CGFloat(levelsPerRow - 1) * spacing / 2 + CGFloat(col) * spacing
            let buttonY = self.size.height/2 + 120 - CGFloat(row) * spacing

            // 检查关卡状态
            let isCompleted = progressManager.isLevelCompleted(levelIndex: i)
            let isUnlocked = progressManager.isLevelUnlocked(levelIndex: i)
            let isCurrent = i == currentLevelIndex

            // 关卡按钮背景 - 根据状态设置颜色
            let buttonBackground = SKShapeNode(rectOf: CGSize(width: buttonSize, height: buttonSize), cornerRadius: 10)
            if isCurrent {
                buttonBackground.fillColor = SKColor.systemBlue // 当前关卡高亮
                buttonBackground.strokeColor = SKColor.white
            } else if isCompleted {
                buttonBackground.fillColor = SKColor.systemGreen // 已完成关卡
                buttonBackground.strokeColor = SKColor.white
            } else if isUnlocked {
                buttonBackground.fillColor = SKColor.systemOrange // 可游玩关卡
                buttonBackground.strokeColor = SKColor.white
            } else {
                buttonBackground.fillColor = SKColor.systemGray // 锁定关卡
                buttonBackground.strokeColor = SKColor.darkGray
            }
            buttonBackground.lineWidth = 2
            buttonBackground.position = CGPoint(x: buttonX, y: buttonY)
            buttonBackground.zPosition = 202
            buttonBackground.name = isUnlocked ? "levelButton_\(i)" : "lockedLevel_\(i)"
            self.addChild(buttonBackground)

            // 关卡数字
            let levelNumber = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
            levelNumber.text = "\(i + 1)"
            levelNumber.fontSize = 20
            levelNumber.fontColor = isUnlocked ? SKColor.white : SKColor.gray
            levelNumber.horizontalAlignmentMode = .center
            levelNumber.verticalAlignmentMode = .center
            levelNumber.position = buttonBackground.position
            levelNumber.zPosition = 203
            levelNumber.name = isUnlocked ? "levelNumber_\(i)" : "lockedNumber_\(i)"
            self.addChild(levelNumber)

            // 为已完成的关卡添加星星标记
            if isCompleted {
                let star = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
                star.text = "⭐"
                star.fontSize = 16
                star.position = CGPoint(x: buttonX + 15, y: buttonY + 15)
                star.zPosition = 204
                star.name = "levelStar_\(i)"
                self.addChild(star)
            }

            // 为教学关卡添加特殊标记
            if i < tutorialLevels {
                let tutorialMark = SKLabelNode(fontNamed: "HelveticaNeue")
                tutorialMark.text = "📚"
                tutorialMark.fontSize = 12
                tutorialMark.position = CGPoint(x: buttonX - 15, y: buttonY + 15)
                tutorialMark.zPosition = 204
                tutorialMark.name = "tutorialMark_\(i)"
                self.addChild(tutorialMark)
            }
        }

        // 关闭按钮
        let closeButton = SKShapeNode(rectOf: CGSize(width: 100, height: 40), cornerRadius: 20)
        closeButton.fillColor = SKColor.systemRed
        closeButton.strokeColor = SKColor.white
        closeButton.lineWidth = 2
        closeButton.position = CGPoint(x: self.size.width/2, y: self.size.height/2 - 200)
        closeButton.zPosition = 202
        closeButton.name = "levelSelectClose"
        self.addChild(closeButton)

        let closeLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        closeLabel.text = "关闭"
        closeLabel.fontSize = 18
        closeLabel.fontColor = SKColor.white
        closeLabel.horizontalAlignmentMode = .center
        closeLabel.verticalAlignmentMode = .center
        closeLabel.position = closeButton.position
        closeLabel.zPosition = 203
        closeLabel.name = "levelSelectCloseLabel"
        self.addChild(closeLabel)

        // 入场动画
        menuPanel.setScale(0)
        let scaleUp = SKAction.scale(to: 1.0, duration: 0.3)
        scaleUp.timingMode = .easeOut
        menuPanel.run(scaleUp)
    }



    // UI设置函数已移至UIManager

    // 重置按钮设置函数已移至UIManager

    // 回退按钮设置函数已移至UIManager

    // Level selection button removed for cleaner UI

    // 按钮动画函数已移至UIManager

    // 音效系统已移至AudioManager

    // MARK: - 创建游戏元素方法

    /// 创建地面瓦片
    private func createGroundTile() -> SKSpriteNode {
        let groundNode = SKSpriteNode(color: SKColor(red: 0.9, green: 0.95, blue: 0.85, alpha: 1.0), size: CGSize(width: tileSize, height: tileSize))
        groundNode.name = "ground"

        // 添加细微的纹理效果
        let border = SKShapeNode(rectOf: groundNode.size, cornerRadius: 2)
        border.strokeColor = SKColor.black.withAlphaComponent(0.1)
        border.lineWidth = 1
        groundNode.addChild(border)

        return groundNode
    }

    /// 创建墙壁瓦片
    private func createWallTile() -> SKSpriteNode {
        let wallNode = SKSpriteNode(color: SKColor(red: 0.3, green: 0.2, blue: 0.1, alpha: 1.0), size: CGSize(width: tileSize, height: tileSize))
        wallNode.name = "wall"
        wallNode.zPosition = 1

        // 添加3D效果
        let highlight = SKShapeNode(rect: CGRect(x: -tileSize/2 + 2, y: -tileSize/2 + 2, width: tileSize - 4, height: tileSize - 4), cornerRadius: 3)
        highlight.fillColor = SKColor.white.withAlphaComponent(0.2)
        highlight.strokeColor = SKColor.black.withAlphaComponent(0.3)
        highlight.lineWidth = 2
        wallNode.addChild(highlight)

        return wallNode
    }

    /// 创建玩家瓦片
    private func createPlayerTile() -> SKSpriteNode {
        let playerNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize * 0.8, height: tileSize * 0.8))
        playerNode.name = "player"
        playerNode.zPosition = 3

        // 创建玩家身体（圆形）
        let body = SKShapeNode(circleOfRadius: tileSize * 0.3)
        body.fillColor = SKColor(red: 1.0, green: 0.6, blue: 0.2, alpha: 1.0) // 橙色
        body.strokeColor = SKColor(red: 0.8, green: 0.4, blue: 0.0, alpha: 1.0)
        body.lineWidth = 3
        playerNode.addChild(body)

        // 添加眼睛
        let leftEye = SKShapeNode(circleOfRadius: 3)
        leftEye.fillColor = .black
        leftEye.position = CGPoint(x: -8, y: 5)
        playerNode.addChild(leftEye)

        let rightEye = SKShapeNode(circleOfRadius: 3)
        rightEye.fillColor = .black
        rightEye.position = CGPoint(x: 8, y: 5)
        playerNode.addChild(rightEye)

        // 添加嘴巴
        let mouth = SKShapeNode(circleOfRadius: 8)
        mouth.fillColor = .clear
        mouth.strokeColor = .black
        mouth.lineWidth = 2
        mouth.position = CGPoint(x: 0, y: -5)
        playerNode.addChild(mouth)

        return playerNode
    }

    /// 创建箱子瓦片 - 改进版本，更好的3D效果
    private func createBoxTile() -> SKSpriteNode {
        let boxNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize * 0.85, height: tileSize * 0.85))
        boxNode.name = "box"
        boxNode.zPosition = 2

        // 创建主体箱子
        let mainBox = SKShapeNode(rect: CGRect(x: -tileSize * 0.4, y: -tileSize * 0.4, width: tileSize * 0.8, height: tileSize * 0.8), cornerRadius: 8)
        mainBox.fillColor = SKColor(red: 0.8, green: 0.6, blue: 0.4, alpha: 1.0) // 木箱颜色
        mainBox.strokeColor = SKColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0)
        mainBox.lineWidth = 3
        boxNode.addChild(mainBox)

        // 添加顶部高光效果
        let topHighlight = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: tileSize * 0.15, width: tileSize * 0.7, height: tileSize * 0.2), cornerRadius: 4)
        topHighlight.fillColor = SKColor.white.withAlphaComponent(0.4)
        topHighlight.strokeColor = .clear
        boxNode.addChild(topHighlight)

        // 添加左侧高光效果
        let leftHighlight = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: -tileSize * 0.35, width: tileSize * 0.15, height: tileSize * 0.7), cornerRadius: 4)
        leftHighlight.fillColor = SKColor.white.withAlphaComponent(0.25)
        leftHighlight.strokeColor = .clear
        boxNode.addChild(leftHighlight)

        // 添加右侧阴影效果
        let rightShadow = SKShapeNode(rect: CGRect(x: tileSize * 0.2, y: -tileSize * 0.35, width: tileSize * 0.15, height: tileSize * 0.7), cornerRadius: 4)
        rightShadow.fillColor = SKColor.black.withAlphaComponent(0.2)
        rightShadow.strokeColor = .clear
        boxNode.addChild(rightShadow)

        // 添加底部阴影效果
        let bottomShadow = SKShapeNode(rect: CGRect(x: -tileSize * 0.35, y: -tileSize * 0.35, width: tileSize * 0.7, height: tileSize * 0.15), cornerRadius: 4)
        bottomShadow.fillColor = SKColor.black.withAlphaComponent(0.2)
        bottomShadow.strokeColor = .clear
        boxNode.addChild(bottomShadow)

        // 根据README要求：箱子带十字标记
        let crossSize: CGFloat = tileSize * 0.4
        let crossWidth: CGFloat = tileSize * 0.08

        // 创建十字标记的垂直部分
        let verticalCross = SKShapeNode(rect: CGRect(x: -crossWidth/2, y: -crossSize/2, width: crossWidth, height: crossSize))
        verticalCross.fillColor = SKColor(red: 0.4, green: 0.3, blue: 0.2, alpha: 0.8)
        verticalCross.strokeColor = SKColor.black.withAlphaComponent(0.3)
        verticalCross.lineWidth = 1
        boxNode.addChild(verticalCross)

        // 创建十字标记的水平部分
        let horizontalCross = SKShapeNode(rect: CGRect(x: -crossSize/2, y: -crossWidth/2, width: crossSize, height: crossWidth))
        horizontalCross.fillColor = SKColor(red: 0.4, green: 0.3, blue: 0.2, alpha: 0.8)
        horizontalCross.strokeColor = SKColor.black.withAlphaComponent(0.3)
        horizontalCross.lineWidth = 1
        boxNode.addChild(horizontalCross)

        // 为十字标记添加高光效果
        let crossHighlightV = SKShapeNode(rect: CGRect(x: -crossWidth/4, y: -crossSize/2, width: crossWidth/2, height: crossSize))
        crossHighlightV.fillColor = SKColor.white.withAlphaComponent(0.3)
        crossHighlightV.strokeColor = .clear
        boxNode.addChild(crossHighlightV)

        let crossHighlightH = SKShapeNode(rect: CGRect(x: -crossSize/2, y: -crossWidth/4, width: crossSize, height: crossWidth/2))
        crossHighlightH.fillColor = SKColor.white.withAlphaComponent(0.3)
        crossHighlightH.strokeColor = .clear
        boxNode.addChild(crossHighlightH)

        return boxNode
    }

    /// 创建目标瓦片
    private func createTargetTile() -> SKSpriteNode {
        let targetNode = SKSpriteNode(color: .clear, size: CGSize(width: tileSize, height: tileSize))
        targetNode.name = "target"
        targetNode.zPosition = 1

        // 创建目标圆环
        let outerCircle = SKShapeNode(circleOfRadius: tileSize * 0.35)
        outerCircle.fillColor = SKColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 0.6)
        outerCircle.strokeColor = SKColor(red: 0.0, green: 0.6, blue: 0.0, alpha: 1.0)
        outerCircle.lineWidth = 3
        targetNode.addChild(outerCircle)

        let innerCircle = SKShapeNode(circleOfRadius: tileSize * 0.15)
        innerCircle.fillColor = SKColor(red: 0.0, green: 0.9, blue: 0.0, alpha: 0.8)
        innerCircle.strokeColor = .clear
        targetNode.addChild(innerCircle)

        // 添加闪烁动画
        let fadeOut = SKAction.fadeAlpha(to: 0.3, duration: 1.0)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 1.0)
        let pulse = SKAction.sequence([fadeOut, fadeIn])
        let repeatPulse = SKAction.repeatForever(pulse)
        targetNode.run(repeatPulse)

        return targetNode
    }

    // MARK: - 点击移动处理

    /// 检查点击位置是否在游戏区域内
    /// - Parameter location: 点击位置
    /// - Returns: 如果在游戏区域内返回true，否则返回false
    func isLocationInGameArea(_ location: CGPoint) -> Bool {
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        let sceneWidth = CGFloat(levelWidth) * tileSize
        let sceneHeight = CGFloat(levelHeight) * tileSize

        let offsetX = (self.size.width - sceneWidth) / 2.0
        let offsetY = (self.size.height - sceneHeight) / 2.0

        let gameAreaMinX = offsetX
        let gameAreaMaxX = offsetX + sceneWidth
        let gameAreaMinY = offsetY
        let gameAreaMaxY = offsetY + sceneHeight

        return location.x >= gameAreaMinX && location.x <= gameAreaMaxX &&
               location.y >= gameAreaMinY && location.y <= gameAreaMaxY
    }

    // 移动逻辑已移至TouchManager，删除重复代码

    /// 显示移动路径
    /// - Parameters:
    ///   - startPos: 起始位置
    ///   - direction: 移动方向
    func showMovementPath(from startPos: CGPoint, direction: CGVector) {
        // 清除之前的路径
        clearMovementPath()

        let targetPos = CGPoint(x: startPos.x + direction.dx, y: startPos.y + direction.dy)

        // 检查目标位置是否有效
        if !isWall(at: targetPos) {
            let pathNode = SKSpriteNode(color: SKColor.yellow.withAlphaComponent(0.5), size: CGSize(width: tileSize * 0.3, height: tileSize * 0.3))
            pathNode.position = scenePosition(for: targetPos)
            pathNode.zPosition = 10
            pathNode.name = "path"

            // 添加闪烁动画
            let fadeOut = SKAction.fadeAlpha(to: 0.2, duration: 0.3)
            let fadeIn = SKAction.fadeAlpha(to: 0.8, duration: 0.3)
            let blink = SKAction.sequence([fadeOut, fadeIn])
            pathNode.run(SKAction.repeatForever(blink))

            self.addChild(pathNode)
            pathNodes.append(pathNode)

            // 1秒后自动清除路径
            let wait = SKAction.wait(forDuration: 1.0)
            let clear = SKAction.run { [weak self] in
                self?.clearMovementPath()
            }
            self.run(SKAction.sequence([wait, clear]))
        }
    }

    /// 清除移动路径显示
    private func clearMovementPath() {
        for pathNode in pathNodes {
            pathNode.removeFromParent()
        }
        pathNodes.removeAll()
    }

    // TODO: 实现广告和内购相关功能

    // TODO: 实现更多主题场景

    // TODO: 实现提示系统

    // TODO: 实现思维训练设计（黄金星星、能力报告）

    // MARK: - Public Methods for Managers

    /// 显示移动路径（管理器版本）
    /// - Parameters:
    ///   - startPos: 起始位置
    ///   - direction: 移动方向
    func showMovementPathManager(from startPos: CGPoint, direction: CGVector) {
        animationManager.showMovementPath(from: startPos, direction: direction)
    }

    /// 显示死锁警告（管理器版本）
    func showDeadlockAlertManager() {
        animationManager.showDeadlockAlert()
    }

    /// 关闭死锁警告（管理器版本）
    func closeDeadlockAlertManager() {
        animationManager.closeDeadlockAlert()
    }

    /// 播放胜利动画和音效（管理器版本）
    func playWinAnimationAndSoundManager() {
        animationManager.playWinAnimationAndSound()
    }

}
