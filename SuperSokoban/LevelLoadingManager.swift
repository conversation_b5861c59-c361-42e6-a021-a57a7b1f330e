//
//  LevelLoadingManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 关卡加载管理器 - 负责关卡数据的加载和场景设置
class LevelLoadingManager {

    // MARK: - Properties
    private weak var gameScene: GameScene?
    private var screenAdaptationManager: ScreenAdaptationManager!

    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
        self.screenAdaptationManager = ScreenAdaptationManager(gameScene: gameScene)
    }
    
    // MARK: - Public Methods
    
    /// 加载指定索引的关卡
    /// - Parameter index: 关卡索引
    func loadLevel(index: Int) {
        guard let scene = gameScene else { return }
        
        print("[LevelLoadingManager] Attempting to load level at index: \(index)")
        
        // 根据用户要求：前5关是写死的教学关卡，其他关卡使用动态生成
        let levelData = getLevelData(for: index)
        
        if let levelData = levelData {
            scene.levelData = levelData
            scene.currentLevelIndex = index
            
            // 增加关卡尝试次数
            scene.progressManager.incrementAttempts(levelIndex: index)
            
            // 根据关卡调整所有元素大小
            adjustSizesForLevel(levelData: levelData, index: index)
            
            print("[LevelLoadingManager] Level data loaded successfully for index \(index)")
            setupLevel(with: levelData)
            
            // 保存初始状态到管理器
            saveInitialStateToManager()
        } else {
            print("[LevelLoadingManager] 无法加载关卡索引: \(index)。可能是最后一个关卡了。")
            showGameCompletedScreen()
        }
    }
    
    /// 设置关卡场景
    /// - Parameter levelData: 关卡数据
    func setupLevel(with levelData: [[Character]]) {
        guard let scene = gameScene else { return }
        
        print("[LevelLoadingManager] Setting up level with current data...")
        
        // 清理旧的节点
        cleanupOldLevel()
        
        // 重新设置统一的渐变背景
        scene.setupGradientBackground()
        
        // 使用UIManager设置所有UI元素
        scene.uiManager.setupAllUIElements(safeAreaInsets: scene.safeAreaInsets, currentLevel: scene.currentLevelIndex)
        
        // 创建游戏元素
        createGameElements(with: levelData)
        
        print("[LevelLoadingManager] Level setup completed")
    }
    
    // MARK: - Private Methods
    
    /// 获取关卡数据 - 全部使用LevelGenerator动态生成
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据
    private func getLevelData(for index: Int) -> [[Character]]? {
        guard let scene = gameScene else { return nil }

        // 所有关卡都使用LevelGenerator动态生成
        if index < scene.levelGenerator.numberOfLevels {
            let levelData = scene.levelGenerator.getLevel(at: index)
            print("[LevelLoadingManager] Loading dynamic level from LevelGenerator: level \(index + 1) (动态生成关卡)")
            return levelData
        } else {
            // 如果超出了动态生成的范围，循环使用动态关卡
            let cycleIndex = index % scene.levelGenerator.numberOfLevels
            let levelData = scene.levelGenerator.getLevel(at: cycleIndex)
            print("[LevelLoadingManager] Loading cycled dynamic level: level \(index + 1) (循环第\(cycleIndex + 1)关)")
            return levelData
        }
    }
    
    /// 调整所有元素大小以适配屏幕
    /// - Parameters:
    ///   - levelData: 关卡数据
    ///   - index: 关卡索引
    private func adjustSizesForLevel(levelData: [[Character]], index: Int) {
        guard let scene = gameScene else { return }

        // 使用屏幕适配管理器计算适配后的尺寸
        let adaptedSizes = screenAdaptationManager.calculateAdaptedSizes(for: levelData, levelIndex: index)

        // 更新场景中的尺寸信息
        scene.tileSize = adaptedSizes.tileSize
        scene.adaptedSizes = adaptedSizes

        print("[LevelLoadingManager] Adjusted sizes for level \(index + 1):")
        print("  - Tile size: \(adaptedSizes.tileSize)")
        print("  - Player size: \(adaptedSizes.playerSize)")
        print("  - Box size: \(adaptedSizes.boxSize)")
        print("  - UI scale: \(adaptedSizes.uiElementScale)")
    }
    
    /// 清理旧关卡
    private func cleanupOldLevel() {
        guard let scene = gameScene else { return }
        
        // 清除所有旧节点，包括UI元素
        scene.removeAllChildren()
        scene.boxes.removeAll()
        scene.walls.removeAll()
        scene.targets.removeAll()
        scene.groundTiles.removeAll()
        scene.player = nil
    }
    
    /// 创建游戏元素
    /// - Parameter levelData: 关卡数据
    private func createGameElements(with levelData: [[Character]]) {
        guard let scene = gameScene else { return }
        
        let levelHeight = levelData.count
        let levelWidth = levelData.first?.count ?? 0
        
        // 使用屏幕适配管理器计算起始位置
        let startPosition = screenAdaptationManager.getGameAreaStartPosition(
            levelWidth: levelWidth,
            levelHeight: levelHeight,
            tileSize: scene.tileSize
        )
        
        // 首先创建网格背景
        createGameGrid(scene: scene, startPosition: startPosition, levelWidth: levelWidth, levelHeight: levelHeight)

        // 遍历关卡数据，创建对应的游戏元素
        for (rowIndex, row) in levelData.enumerated() {
            for (colIndex, cell) in row.enumerated() {
                let x = startPosition.x + CGFloat(colIndex) * scene.tileSize
                let y = startPosition.y + CGFloat(levelHeight - 1 - rowIndex) * scene.tileSize // 翻转Y轴
                let position = CGPoint(x: x, y: y)

                // 首先创建地面瓦片（除了墙壁）
                if cell != "W" {
                    let groundTile = createGroundTile(at: position)
                    scene.addChild(groundTile)
                    scene.groundTiles.append(groundTile)
                }
                
                // 根据字符创建对应的游戏元素
                switch cell {
                case "W":
                    let wall = createWall(at: position)
                    scene.addChild(wall)
                    scene.walls.append(wall)
                    
                case "P":
                    let player = createPlayer(at: position)
                    scene.addChild(player)
                    scene.player = player
                    
                case "B":
                    let box = createBox(at: position)
                    scene.addChild(box)
                    scene.boxes.append(box)
                    
                case "T":
                    let target = createTarget(at: position)
                    scene.addChild(target)
                    scene.targets.append(target)
                    
                default:
                    break // "G" 或其他字符，只有地面瓦片
                }
            }
        }
        
        print("[LevelLoadingManager] Created \(scene.walls.count) walls, \(scene.boxes.count) boxes, \(scene.targets.count) targets")
    }
    
    /// 创建地面瓦片
    private func createGroundTile(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene,
              let adaptedSizes = scene.adaptedSizes else { return SKSpriteNode() }

        let groundTile = SKSpriteNode(
            color: SKColor.lightGray.withAlphaComponent(0.3),
            size: CGSize(width: adaptedSizes.groundSize, height: adaptedSizes.groundSize)
        )
        groundTile.position = position
        groundTile.zPosition = 1
        groundTile.name = "groundTile"

        return groundTile
    }
    
    /// 创建墙壁
    private func createWall(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene,
              let adaptedSizes = scene.adaptedSizes else { return SKSpriteNode() }

        let wall = SKSpriteNode(
            color: SKColor.brown,
            size: CGSize(width: adaptedSizes.wallSize, height: adaptedSizes.wallSize)
        )
        wall.position = position
        wall.zPosition = 10
        wall.name = "wall"

        // 添加3D效果，高度根据瓦片大小调整
        let effectHeight = max(4, adaptedSizes.wallSize * 0.1)

        let highlight = SKSpriteNode(
            color: SKColor.white.withAlphaComponent(0.3),
            size: CGSize(width: adaptedSizes.wallSize, height: effectHeight)
        )
        highlight.position = CGPoint(x: 0, y: adaptedSizes.wallSize/2 - effectHeight/2)
        highlight.zPosition = 1
        wall.addChild(highlight)

        let shadow = SKSpriteNode(
            color: SKColor.black.withAlphaComponent(0.3),
            size: CGSize(width: adaptedSizes.wallSize, height: effectHeight)
        )
        shadow.position = CGPoint(x: 0, y: -adaptedSizes.wallSize/2 + effectHeight/2)
        shadow.zPosition = 1
        wall.addChild(shadow)

        return wall
    }
    
    /// 创建玩家
    private func createPlayer(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene,
              let adaptedSizes = scene.adaptedSizes else { return SKSpriteNode() }

        let playerSize = CGSize(width: adaptedSizes.playerSize, height: adaptedSizes.playerSize)
        // 使用更鲜艳的橙色，增强可见性
        let player = SKSpriteNode(color: SKColor(red: 1.0, green: 0.6, blue: 0.0, alpha: 1.0), size: playerSize)
        player.position = position
        player.zPosition = 20
        player.name = "player"

        // 创建圆形外观，使用相同的鲜艳橙色
        let circleTexture = createCircleTexture(size: playerSize, color: SKColor(red: 1.0, green: 0.6, blue: 0.0, alpha: 1.0))
        player.texture = circleTexture

        // 根据玩家大小调整眼睛大小和位置
        let eyeSize = max(4, adaptedSizes.playerSize * 0.15)
        let eyeOffset = adaptedSizes.playerSize * 0.2

        let leftEye = SKSpriteNode(color: SKColor.black, size: CGSize(width: eyeSize, height: eyeSize))
        leftEye.position = CGPoint(x: -eyeOffset, y: eyeOffset * 0.5)
        leftEye.zPosition = 1
        player.addChild(leftEye)

        let rightEye = SKSpriteNode(color: SKColor.black, size: CGSize(width: eyeSize, height: eyeSize))
        rightEye.position = CGPoint(x: eyeOffset, y: eyeOffset * 0.5)
        rightEye.zPosition = 1
        player.addChild(rightEye)

        // 添加嘴巴，大小根据玩家尺寸调整
        let mouth = SKShapeNode(path: createSmilePath(radius: adaptedSizes.playerSize * 0.2))
        mouth.strokeColor = SKColor.black
        mouth.lineWidth = max(1, adaptedSizes.playerSize * 0.05)
        mouth.position = CGPoint(x: 0, y: -eyeOffset * 0.3)
        mouth.zPosition = 1
        player.addChild(mouth)

        return player
    }
    
    /// 创建箱子
    private func createBox(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene,
              let adaptedSizes = scene.adaptedSizes else { return SKSpriteNode() }

        let boxSize = CGSize(width: adaptedSizes.boxSize, height: adaptedSizes.boxSize)
        // 使用更深的棕色，增强对比度
        let box = SKSpriteNode(color: SKColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0), size: boxSize)
        box.position = position
        box.zPosition = 15
        box.name = "box"

        // 添加3D效果，高度根据箱子大小调整
        let effectHeight = max(4, adaptedSizes.boxSize * 0.15)

        let highlight = SKSpriteNode(
            color: SKColor.white.withAlphaComponent(0.4),
            size: CGSize(width: adaptedSizes.boxSize, height: effectHeight)
        )
        highlight.position = CGPoint(x: 0, y: adaptedSizes.boxSize/2 - effectHeight/2)
        highlight.zPosition = 1
        box.addChild(highlight)

        let shadow = SKSpriteNode(
            color: SKColor.black.withAlphaComponent(0.4),
            size: CGSize(width: adaptedSizes.boxSize, height: effectHeight)
        )
        shadow.position = CGPoint(x: 0, y: -adaptedSizes.boxSize/2 + effectHeight/2)
        shadow.zPosition = 1
        box.addChild(shadow)

        // 添加十字标记（根据README要求），大小根据箱子尺寸调整
        let crossThickness = max(2, adaptedSizes.boxSize * 0.08)
        let crossLength = adaptedSizes.boxSize * 0.6

        let crossVertical = SKSpriteNode(
            color: SKColor.white.withAlphaComponent(0.8),
            size: CGSize(width: crossThickness, height: crossLength)
        )
        crossVertical.position = CGPoint(x: 0, y: 0)
        crossVertical.zPosition = 1
        box.addChild(crossVertical)

        let crossHorizontal = SKSpriteNode(
            color: SKColor.white.withAlphaComponent(0.8),
            size: CGSize(width: crossLength, height: crossThickness)
        )
        crossHorizontal.position = CGPoint(x: 0, y: 0)
        crossHorizontal.zPosition = 1
        box.addChild(crossHorizontal)

        return box
    }
    
    /// 创建目标点
    private func createTarget(at position: CGPoint) -> SKSpriteNode {
        guard let scene = gameScene,
              let adaptedSizes = scene.adaptedSizes else { return SKSpriteNode() }

        let targetSize = CGSize(width: adaptedSizes.targetSize, height: adaptedSizes.targetSize)
        let target = SKSpriteNode(color: SKColor.clear, size: targetSize)
        target.position = position
        target.zPosition = 5
        target.name = "target"

        // 创建更醒目的绿色圆环，大小根据目标点尺寸调整
        let outerRadius = adaptedSizes.targetSize * 0.45 // 增大外圆半径
        let innerRadius = adaptedSizes.targetSize * 0.25 // 减小内圆半径，增加环的厚度
        let ringPath = createRingPath(outerRadius: outerRadius, innerRadius: innerRadius)
        let ring = SKShapeNode(path: ringPath)
        ring.strokeColor = SKColor.systemGreen
        ring.fillColor = SKColor.systemGreen.withAlphaComponent(0.4) // 增加透明度
        ring.lineWidth = max(3, adaptedSizes.targetSize * 0.1) // 增加线宽
        ring.zPosition = 1
        target.addChild(ring)

        // 添加内部高亮圆点，增强可见性
        let centerDot = SKShapeNode(circleOfRadius: adaptedSizes.targetSize * 0.12)
        centerDot.fillColor = SKColor.systemGreen.withAlphaComponent(0.6)
        centerDot.strokeColor = SKColor.white
        centerDot.lineWidth = max(1, adaptedSizes.targetSize * 0.03)
        centerDot.zPosition = 2
        target.addChild(centerDot)

        // 添加闪烁动画
        let fadeOut = SKAction.fadeAlpha(to: 0.3, duration: 1.0)
        let fadeIn = SKAction.fadeAlpha(to: 1.0, duration: 1.0)
        let pulse = SKAction.sequence([fadeOut, fadeIn])
        let repeatPulse = SKAction.repeatForever(pulse)
        target.run(repeatPulse)

        return target
    }
    
    /// 保存初始状态到管理器
    private func saveInitialStateToManager() {
        guard let scene = gameScene,
              let playerNode = scene.player else { return }
        
        let initialPlayerPosition = scene.gridPosition(for: playerNode.position)
        let initialBoxPositions = scene.boxes.map { scene.gridPosition(for: $0.position) }
        
        scene.gameStateManager.saveInitialState(
            playerPosition: initialPlayerPosition,
            boxPositions: initialBoxPositions
        )
        
        print("[LevelLoadingManager] Initial state saved to GameStateManager")
    }
    
    /// 显示游戏完成界面
    private func showGameCompletedScreen() {
        guard let scene = gameScene else { return }
        
        // 创建游戏完成界面
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.8), size: scene.size)
        overlay.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        overlay.zPosition = 200
        overlay.name = "gameCompletedOverlay"
        scene.addChild(overlay)
        
        let completedLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        completedLabel.text = "🎉 恭喜完成所有关卡! 🎉"
        completedLabel.fontSize = 28
        completedLabel.fontColor = SKColor.systemGreen
        completedLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        completedLabel.zPosition = 201
        completedLabel.name = "gameCompletedLabel"
        scene.addChild(completedLabel)
        
        // 添加彩色动画
        let colorCycle = createColorCycleAnimation()
        completedLabel.run(colorCycle)
    }
    
    // MARK: - Helper Methods
    
    /// 创建圆形纹理
    private func createCircleTexture(size: CGSize, color: SKColor) -> SKTexture {
        let renderer = UIGraphicsImageRenderer(size: size)
        let image = renderer.image { context in
            color.setFill()
            let rect = CGRect(origin: .zero, size: size)
            context.cgContext.fillEllipse(in: rect)
        }
        return SKTexture(image: image)
    }
    
    /// 创建笑脸路径
    private func createSmilePath(radius: CGFloat = 8) -> CGPath {
        let path = CGMutablePath()
        path.addArc(center: CGPoint.zero, radius: radius, startAngle: .pi * 0.2, endAngle: .pi * 0.8, clockwise: false)
        return path
    }
    
    /// 创建圆环路径
    private func createRingPath(outerRadius: CGFloat, innerRadius: CGFloat) -> CGPath {
        let path = CGMutablePath()
        path.addArc(center: CGPoint.zero, radius: outerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: false)
        path.addArc(center: CGPoint.zero, radius: innerRadius, startAngle: 0, endAngle: .pi * 2, clockwise: true)
        return path
    }

    /// 创建游戏网格
    private func createGameGrid(scene: GameScene, startPosition: CGPoint, levelWidth: Int, levelHeight: Int) {
        guard let adaptedSizes = scene.adaptedSizes else { return }

        let tileSize = adaptedSizes.tileSize
        let gridColor = SKColor.white.withAlphaComponent(0.2)
        let lineWidth: CGFloat = max(0.5, tileSize * 0.01)

        // 创建网格容器
        let gridContainer = SKNode()
        gridContainer.name = "gameGrid"
        gridContainer.zPosition = 0.5 // 在地面之上，游戏元素之下
        scene.addChild(gridContainer)

        // 创建垂直线
        for i in 0...levelWidth {
            let line = SKShapeNode()
            let path = CGMutablePath()
            let x = startPosition.x - tileSize/2 + CGFloat(i) * tileSize
            let startY = startPosition.y - tileSize/2
            let endY = startPosition.y - tileSize/2 + CGFloat(levelHeight) * tileSize

            path.move(to: CGPoint(x: x, y: startY))
            path.addLine(to: CGPoint(x: x, y: endY))

            line.path = path
            line.strokeColor = gridColor
            line.lineWidth = lineWidth
            gridContainer.addChild(line)
        }

        // 创建水平线
        for i in 0...levelHeight {
            let line = SKShapeNode()
            let path = CGMutablePath()
            let y = startPosition.y - tileSize/2 + CGFloat(i) * tileSize
            let startX = startPosition.x - tileSize/2
            let endX = startPosition.x - tileSize/2 + CGFloat(levelWidth) * tileSize

            path.move(to: CGPoint(x: startX, y: y))
            path.addLine(to: CGPoint(x: endX, y: y))

            line.path = path
            line.strokeColor = gridColor
            line.lineWidth = lineWidth
            gridContainer.addChild(line)
        }

        print("[LevelLoadingManager] Created game grid: \(levelWidth)x\(levelHeight)")
    }

    /// 创建颜色循环动画
    private func createColorCycleAnimation() -> SKAction {
        let colors = [SKColor.systemRed, SKColor.systemOrange, SKColor.systemYellow, SKColor.systemGreen, SKColor.systemBlue, SKColor.systemPurple]
        var colorActions: [SKAction] = []

        for color in colors {
            let colorAction = SKAction.colorize(with: color, colorBlendFactor: 1.0, duration: 0.3)
            colorActions.append(colorAction)
        }

        let colorSequence = SKAction.sequence(colorActions)
        return SKAction.repeatForever(colorSequence)
    }
}
