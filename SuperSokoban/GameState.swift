import Foundation

/// 游戏状态管理类
class GameState {
    
    // MARK: - Properties
    
    /// 当前关卡数据
    private(set) var grid: [[TileType]] = []
    
    /// 网格尺寸
    private(set) var rows: Int = 0
    private(set) var cols: Int = 0
    
    /// 玩家位置
    private(set) var playerPosition: GridPosition = GridPosition(row: 0, col: 0)
    
    /// 当前关卡索引
    private(set) var currentLevel: Int = 0
    
    /// 移动步数
    private(set) var moveCount: Int = 0
    
    /// 历史状态（用于撤销）
    private var history: [GameStateSnapshot] = []
    
    /// 关卡管理器
    private let levelManager = LevelManager()
    
    /// 关卡生成器
    private let levelGenerator = LevelGenerator()
    
    // MARK: - Initialization
    
    init() {
        loadLevel(0)
    }
    
    // MARK: - Public Methods
    
    /// 加载指定关卡
    func loadLevel(_ levelIndex: Int) {
        currentLevel = levelIndex
        moveCount = 0
        history.removeAll()

        // 使用关卡生成器生成关卡
        guard let levelData = levelGenerator.getLevel(at: levelIndex) else {
            print("[GameState] ❌ 无法加载关卡 \(levelIndex)")
            // 加载默认关卡
            loadDefaultLevel()
            return
        }

        parseLevel(levelData)

        print("[GameState] 加载关卡 \(levelIndex + 1), 网格大小: \(rows)x\(cols)")
    }
    
    /// 获取指定位置的瓦片类型
    func getTileType(at position: GridPosition) -> TileType {
        guard isValidPosition(position) else { return .wall }
        return grid[position.row][position.col]
    }
    
    /// 设置指定位置的瓦片类型
    private func setTileType(at position: GridPosition, to type: TileType) {
        guard isValidPosition(position) else { return }
        grid[position.row][position.col] = type
    }
    
    /// 尝试移动玩家
    func attemptMove(to targetPosition: GridPosition) -> Bool {
        guard let direction = Direction.from(playerPosition, to: targetPosition) else {
            return false
        }
        
        return performMove(direction: direction)
    }
    
    /// 执行移动
    private func performMove(direction: Direction) -> Bool {
        let newPlayerPosition = playerPosition.moved(by: direction)
        
        // 检查新位置是否有效
        guard isValidPosition(newPlayerPosition) else { return false }
        
        let targetTile = getTileType(at: newPlayerPosition)
        
        // 不能移动到墙壁
        if targetTile == .wall { return false }
        
        // 保存当前状态用于撤销
        saveCurrentState()
        
        // 如果目标位置有箱子，尝试推箱子
        if targetTile.isBox {
            let boxNewPosition = newPlayerPosition.moved(by: direction)
            guard canPushBox(to: boxNewPosition) else {
                // 撤销保存的状态
                history.removeLast()
                return false
            }
            
            // 推箱子
            pushBox(from: newPlayerPosition, to: boxNewPosition)
        }
        
        // 移动玩家
        movePlayer(to: newPlayerPosition)
        
        moveCount += 1
        return true
    }
    
    /// 检查是否可以推箱子到指定位置
    private func canPushBox(to position: GridPosition) -> Bool {
        guard isValidPosition(position) else { return false }
        
        let targetTile = getTileType(at: position)
        return targetTile != .wall && !targetTile.isBox
    }
    
    /// 推箱子
    private func pushBox(from: GridPosition, to: GridPosition) {
        let fromTile = getTileType(at: from)
        let toTile = getTileType(at: to)
        
        // 移除原位置的箱子
        if fromTile == .boxOnTarget {
            setTileType(at: from, to: .target)
        } else {
            setTileType(at: from, to: .ground)
        }
        
        // 在新位置放置箱子
        if toTile.isTarget {
            setTileType(at: to, to: .boxOnTarget)
        } else {
            setTileType(at: to, to: .box)
        }
    }
    
    /// 移动玩家
    private func movePlayer(to newPosition: GridPosition) {
        let oldTile = getTileType(at: playerPosition)
        let newTile = getTileType(at: newPosition)
        
        // 清除原位置的玩家
        if oldTile == .playerOnTarget {
            setTileType(at: playerPosition, to: .target)
        } else {
            setTileType(at: playerPosition, to: .ground)
        }
        
        // 在新位置放置玩家
        if newTile.isTarget {
            setTileType(at: newPosition, to: .playerOnTarget)
        } else {
            setTileType(at: newPosition, to: .player)
        }
        
        playerPosition = newPosition
    }
    
    /// 检查是否胜利
    func checkWinCondition() -> Bool {
        for row in 0..<rows {
            for col in 0..<cols {
                let tile = grid[row][col]
                if tile == .box { // 还有箱子不在目标点上
                    return false
                }
            }
        }
        return true
    }
    
    /// 撤销上一步
    func undoLastMove() -> Bool {
        guard let lastState = history.popLast() else { return false }
        
        grid = lastState.grid
        playerPosition = lastState.playerPosition
        moveCount = lastState.moveCount
        
        return true
    }
    
    /// 重置当前关卡
    func resetLevel() {
        loadLevel(currentLevel)
    }
    
    /// 下一关
    func nextLevel() {
        loadLevel(currentLevel + 1)
    }
    
    // MARK: - Private Methods

    /// 加载默认关卡
    private func loadDefaultLevel() {
        let defaultLevel: [[Character]] = [
            ["W", "W", "W", "W", "W"],
            ["W", "P", "G", "T", "W"],
            ["W", "G", "B", "G", "W"],
            ["W", "G", "G", "G", "W"],
            ["W", "W", "W", "W", "W"]
        ]
        parseLevel(defaultLevel)
    }

    /// 解析关卡数据
    private func parseLevel(_ levelData: [[Character]]) {
        rows = levelData.count
        cols = levelData.first?.count ?? 0
        
        grid = Array(repeating: Array(repeating: .ground, count: cols), count: rows)
        
        for (row, rowData) in levelData.enumerated() {
            for (col, char) in rowData.enumerated() {
                if let tileType = TileType(rawValue: char) {
                    grid[row][col] = tileType
                    
                    // 记录玩家位置
                    if tileType.isPlayer {
                        playerPosition = GridPosition(row: row, col: col)
                    }
                }
            }
        }
    }
    
    /// 检查位置是否有效
    private func isValidPosition(_ position: GridPosition) -> Bool {
        return position.row >= 0 && position.row < rows &&
               position.col >= 0 && position.col < cols
    }
    
    /// 保存当前状态
    private func saveCurrentState() {
        let snapshot = GameStateSnapshot(
            grid: grid,
            playerPosition: playerPosition,
            moveCount: moveCount
        )
        history.append(snapshot)
        
        // 限制历史记录数量
        if history.count > 50 {
            history.removeFirst()
        }
    }
}

/// 游戏状态快照（用于撤销功能）
private struct GameStateSnapshot {
    let grid: [[TileType]]
    let playerPosition: GridPosition
    let moveCount: Int
}
