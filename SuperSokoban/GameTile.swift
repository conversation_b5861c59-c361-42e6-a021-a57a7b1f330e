import UIKit

/// 游戏瓦片类型枚举
enum TileType: Character, CaseIterable {
    case empty = " "      // 空地
    case wall = "W"       // 墙壁
    case ground = "G"     // 地面
    case player = "P"     // 玩家
    case box = "B"        // 箱子
    case target = "T"     // 目标点
    case boxOnTarget = "*" // 箱子在目标点上
    case playerOnTarget = "+" // 玩家在目标点上
    
    /// 获取瓦片的显示颜色
    var color: UIColor {
        switch self {
        case .empty:
            return .clear
        case .wall:
            return UIColor(red: 0.4, green: 0.2, blue: 0.1, alpha: 1.0)
        case .ground:
            return UIColor(white: 0.95, alpha: 1.0)
        case .player:
            return UIColor(red: 1.0, green: 0.6, blue: 0.0, alpha: 1.0)
        case .box:
            return UIColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0)
        case .target:
            return UIColor.systemGreen.withAlphaComponent(0.3)
        case .boxOnTarget:
            return UIColor.systemGreen
        case .playerOnTarget:
            return UIColor(red: 1.0, green: 0.8, blue: 0.0, alpha: 1.0)
        }
    }
    
    /// 获取瓦片的显示文本
    var displayText: String {
        switch self {
        case .empty, .ground:
            return ""
        case .wall:
            return "🧱"
        case .player, .playerOnTarget:
            return "😊"
        case .box:
            return "📦"
        case .target:
            return "🎯"
        case .boxOnTarget:
            return "✅"
        }
    }
    
    /// 是否可以移动到此瓦片
    var isWalkable: Bool {
        switch self {
        case .wall:
            return false
        default:
            return true
        }
    }
    
    /// 是否是箱子
    var isBox: Bool {
        return self == .box || self == .boxOnTarget
    }
    
    /// 是否是目标点
    var isTarget: Bool {
        return self == .target || self == .boxOnTarget || self == .playerOnTarget
    }
    
    /// 是否是玩家
    var isPlayer: Bool {
        return self == .player || self == .playerOnTarget
    }
}

/// 游戏瓦片数据模型
struct GameTile {
    let row: Int
    let col: Int
    var type: TileType
    
    init(row: Int, col: Int, type: TileType = .empty) {
        self.row = row
        self.col = col
        self.type = type
    }
    
    /// 获取瓦片的索引路径
    var indexPath: IndexPath {
        return IndexPath(item: row * 100 + col, section: 0) // 假设最大100列
    }
}

/// 游戏网格位置
struct GridPosition: Equatable {
    let row: Int
    let col: Int
    
    /// 根据方向获取新位置
    func moved(by direction: Direction) -> GridPosition {
        switch direction {
        case .up:
            return GridPosition(row: row - 1, col: col)
        case .down:
            return GridPosition(row: row + 1, col: col)
        case .left:
            return GridPosition(row: row, col: col - 1)
        case .right:
            return GridPosition(row: row, col: col + 1)
        }
    }
}

/// 移动方向
enum Direction {
    case up, down, left, right
    
    /// 根据两个位置计算方向
    static func from(_ from: GridPosition, to: GridPosition) -> Direction? {
        let rowDiff = to.row - from.row
        let colDiff = to.col - from.col
        
        // 只允许相邻移动
        guard abs(rowDiff) + abs(colDiff) == 1 else { return nil }
        
        if rowDiff == -1 { return .up }
        if rowDiff == 1 { return .down }
        if colDiff == -1 { return .left }
        if colDiff == 1 { return .right }
        
        return nil
    }
}
