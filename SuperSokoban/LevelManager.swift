//
//  LevelManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation

/// 关卡管理器 - 负责关卡数据管理和提供
class LevelManager {
    private var levels: [[[Character]]] = []

    init() {
        setupDefaultLevels()
    }

    /// 设置默认关卡 - 现在使用LevelGenerator动态生成所有关卡
    private func setupDefaultLevels() {
        // 删除写死的关卡，改为使用LevelGenerator动态生成
        // 这样可以提供更多样化和有挑战性的关卡

        // 所有关卡现在都通过LevelGenerator动态生成

        // 现在完全依赖LevelGenerator动态生成关卡，提供无限的游戏内容
    }

    /// 获取指定索引的关卡数据
    /// - Parameter index: 关卡索引
    /// - Returns: 关卡数据 (二维字符数组)，如果索引无效则返回nil
    func getLevel(at index: Int) -> [[Character]]? {
        guard index >= 0 && index < levels.count else {
            return nil
        }
        return levels[index]
    }

    /// 获取总关卡数
    var numberOfLevels: Int {
        return levels.count
    }
    
    // TODO: 添加从文件加载关卡的功能
    // func loadLevelsFromFile(fileName: String) { ... }
}
