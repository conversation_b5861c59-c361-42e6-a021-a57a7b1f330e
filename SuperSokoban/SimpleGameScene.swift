import SpriteKit
import GameplayKit

/// 简化的推箱子游戏场景 - 采用更直接的网格系统
class SimpleGameScene: SKScene {
    
    // MARK: - 游戏数据结构
    
    enum GameTile: Character {
        case empty = " "
        case wall = "#"
        case player = "@"
        case box = "$"
        case target = "."
        case boxOnTarget = "*"
        case playerOnTarget = "+"
    }
    
    // MARK: - 核心属性
    
    var gameGrid: [[GameTile]] = []
    var gridWidth = 0
    var gridHeight = 0
    var playerPos = CGPoint.zero
    
    // 显示设置
    var tileSize: CGFloat = 50
    var gridOrigin = CGPoint.zero
    
    // 游戏状态
    var isMoving = false
    var currentLevel = 0
    
    // 显示容器
    var gameContainer: SKNode!
    
    // 游戏节点
    var playerNode: SKSpriteNode!
    var tileNodes: [[SKSpriteNode?]] = []
    
    // 管理器
    var levelManager = LevelManager()
    var audioManager: AudioManager!
    var uiManager: UIManager!
    
    // MARK: - 初始化
    
    override func didMove(to view: SKView) {
        setupScene()
        setupManagers()
        loadLevel(0)
    }
    
    private func setupScene() {
        backgroundColor = SKColor(red: 0.15, green: 0.15, blue: 0.3, alpha: 1.0)
        
        // 创建游戏容器
        gameContainer = SKNode()
        addChild(gameContainer)
    }
    
    private func setupManagers() {
        // 初始化音频管理器
        audioManager = AudioManager()
        
        // 初始化UI管理器
        uiManager = UIManager(scene: self)
        uiManager.setupUI()
    }
    
    // MARK: - 关卡加载
    
    func loadLevel(_ levelIndex: Int) {
        currentLevel = levelIndex
        
        // 获取关卡数据
        let levelString = levelManager.getLevel(levelIndex)
        parseLevel(levelString)
        
        // 计算显示参数
        calculateDisplayParameters()
        
        // 创建显示元素
        createGameDisplay()
        
        print("[SimpleGameScene] 加载关卡 \(levelIndex), 网格大小: \(gridWidth)x\(gridHeight)")
    }
    
    private func parseLevel(_ levelString: String) {
        let lines = levelString.components(separatedBy: .newlines).filter { !$0.isEmpty }
        gridHeight = lines.count
        gridWidth = lines.map { $0.count }.max() ?? 0
        
        gameGrid = Array(repeating: Array(repeating: .empty, count: gridWidth), count: gridHeight)
        
        for (row, line) in lines.enumerated() {
            for (col, char) in line.enumerated() {
                if let tile = GameTile(rawValue: char) {
                    gameGrid[row][col] = tile
                    
                    // 记录玩家位置
                    if tile == .player || tile == .playerOnTarget {
                        playerPos = CGPoint(x: col, y: row)
                    }
                }
            }
        }
    }
    
    private func calculateDisplayParameters() {
        // 计算合适的瓦片大小
        let availableWidth = size.width * 0.8
        let availableHeight = size.height * 0.7
        
        let maxTileSizeByWidth = availableWidth / CGFloat(gridWidth)
        let maxTileSizeByHeight = availableHeight / CGFloat(gridHeight)
        
        tileSize = min(maxTileSizeByWidth, maxTileSizeByHeight)
        tileSize = max(30, min(tileSize, 80)) // 限制范围
        
        // 计算网格原点（居中显示）
        let totalWidth = CGFloat(gridWidth) * tileSize
        let totalHeight = CGFloat(gridHeight) * tileSize
        
        gridOrigin = CGPoint(
            x: (size.width - totalWidth) / 2,
            y: (size.height - totalHeight) / 2 + 50 // 为UI留空间
        )
    }
    
    private func createGameDisplay() {
        // 清除旧的显示
        gameContainer.removeAllChildren()
        
        // 初始化节点数组
        tileNodes = Array(repeating: Array(repeating: nil, count: gridWidth), count: gridHeight)
        
        // 创建所有瓦片
        for row in 0..<gridHeight {
            for col in 0..<gridWidth {
                let tile = gameGrid[row][col]
                let position = gridToScene(CGPoint(x: col, y: row))
                
                // 创建地面
                if tile != .empty {
                    let groundNode = createTileNode(type: "ground", at: position)
                    gameContainer.addChild(groundNode)
                }
                
                // 创建具体元素
                var node: SKSpriteNode?
                
                switch tile {
                case .wall:
                    node = createTileNode(type: "wall", at: position)
                case .target, .playerOnTarget, .boxOnTarget:
                    node = createTileNode(type: "target", at: position)
                case .box, .boxOnTarget:
                    node = createTileNode(type: "box", at: position)
                case .player, .playerOnTarget:
                    node = createTileNode(type: "player", at: position)
                    playerNode = node
                default:
                    break
                }
                
                if let node = node {
                    gameContainer.addChild(node)
                    tileNodes[row][col] = node
                }
            }
        }
    }
    
    private func createTileNode(type: String, at position: CGPoint) -> SKSpriteNode {
        let node = SKSpriteNode()
        node.size = CGSize(width: tileSize * 0.8, height: tileSize * 0.8)
        node.position = position
        
        // 设置颜色和外观
        switch type {
        case "ground":
            node.color = SKColor(white: 0.9, alpha: 1.0)
        case "wall":
            node.color = SKColor(red: 0.4, green: 0.2, blue: 0.1, alpha: 1.0)
        case "target":
            node.color = SKColor(red: 1.0, green: 0.8, blue: 0.2, alpha: 0.8)
        case "box":
            node.color = SKColor(red: 0.8, green: 0.4, blue: 0.2, alpha: 1.0)
        case "player":
            node.color = SKColor(red: 0.2, green: 0.6, blue: 1.0, alpha: 1.0)
        default:
            node.color = SKColor.gray
        }
        
        return node
    }
    
    // MARK: - 坐标转换
    
    private func gridToScene(_ gridPos: CGPoint) -> CGPoint {
        return CGPoint(
            x: gridOrigin.x + CGFloat(gridPos.x) * tileSize + tileSize / 2,
            y: gridOrigin.y + CGFloat(gridHeight - 1 - Int(gridPos.y)) * tileSize + tileSize / 2
        )
    }
    
    private func sceneToGrid(_ scenePos: CGPoint) -> CGPoint {
        let relativeX = scenePos.x - gridOrigin.x
        let relativeY = scenePos.y - gridOrigin.y
        
        let col = Int(relativeX / tileSize)
        let row = gridHeight - 1 - Int(relativeY / tileSize)
        
        return CGPoint(x: col, y: row)
    }
    
    private func isValidGridPosition(_ pos: CGPoint) -> Bool {
        return pos.x >= 0 && pos.x < gridWidth && pos.y >= 0 && pos.y < gridHeight
    }
    
    // MARK: - 触摸处理
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, !isMoving else { return }
        
        let location = touch.location(in: self)
        let gridPos = sceneToGrid(location)
        
        if isValidGridPosition(gridPos) {
            attemptMove(to: gridPos)
        }
    }
    
    // MARK: - 移动逻辑
    
    private func attemptMove(to targetPos: CGPoint) {
        let direction = CGPoint(
            x: targetPos.x - playerPos.x,
            y: targetPos.y - playerPos.y
        )
        
        // 只允许相邻移动
        if abs(direction.x) + abs(direction.y) != 1 { return }
        
        let newPlayerPos = CGPoint(x: playerPos.x + direction.x, y: playerPos.y + direction.y)
        
        if canMoveTo(newPlayerPos, direction: direction) {
            performMove(to: newPlayerPos, direction: direction)
        }
    }
    
    private func canMoveTo(_ pos: CGPoint, direction: CGPoint) -> Bool {
        guard isValidGridPosition(pos) else { return false }
        
        let tile = gameGrid[Int(pos.y)][Int(pos.x)]
        
        // 不能移动到墙
        if tile == .wall { return false }
        
        // 如果是箱子，检查能否推动
        if tile == .box || tile == .boxOnTarget {
            let boxNewPos = CGPoint(x: pos.x + direction.x, y: pos.y + direction.y)
            guard isValidGridPosition(boxNewPos) else { return false }
            
            let targetTile = gameGrid[Int(boxNewPos.y)][Int(boxNewPos.x)]
            return targetTile != .wall && targetTile != .box && targetTile != .boxOnTarget
        }
        
        return true
    }
    
    private func performMove(to newPos: CGPoint, direction: CGPoint) {
        isMoving = true
        
        let tile = gameGrid[Int(newPos.y)][Int(newPos.x)]
        
        // 处理推箱子
        if tile == .box || tile == .boxOnTarget {
            let boxNewPos = CGPoint(x: newPos.x + direction.x, y: newPos.y + direction.y)
            moveBox(from: newPos, to: boxNewPos)
        }
        
        // 移动玩家
        movePlayer(to: newPos)
        
        // 播放音效
        audioManager?.playSound(.move)

        // 检查胜利条件
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.isMoving = false
            self.checkWinCondition()
        }
    }
    
    private func movePlayer(to newPos: CGPoint) {
        // 更新网格数据
        let oldTile = gameGrid[Int(playerPos.y)][Int(playerPos.x)]
        let newTile = gameGrid[Int(newPos.y)][Int(newPos.x)]
        
        // 清除旧位置
        gameGrid[Int(playerPos.y)][Int(playerPos.x)] = oldTile == .playerOnTarget ? .target : .empty
        
        // 设置新位置
        gameGrid[Int(newPos.y)][Int(newPos.x)] = newTile == .target ? .playerOnTarget : .player
        
        // 更新位置
        playerPos = newPos
        
        // 动画移动
        let targetPosition = gridToScene(newPos)
        let moveAction = SKAction.move(to: targetPosition, duration: 0.2)
        playerNode.run(moveAction)
    }
    
    private func moveBox(from oldPos: CGPoint, to newPos: CGPoint) {
        // 更新网格数据
        let oldTile = gameGrid[Int(oldPos.y)][Int(oldPos.x)]
        let newTile = gameGrid[Int(newPos.y)][Int(newPos.x)]
        
        // 清除旧位置
        gameGrid[Int(oldPos.y)][Int(oldPos.x)] = oldTile == .boxOnTarget ? .target : .empty
        
        // 设置新位置
        gameGrid[Int(newPos.y)][Int(newPos.x)] = newTile == .target ? .boxOnTarget : .box
        
        // 移动节点
        if let boxNode = tileNodes[Int(oldPos.y)][Int(oldPos.x)] {
            let targetPosition = gridToScene(newPos)
            let moveAction = SKAction.move(to: targetPosition, duration: 0.2)
            boxNode.run(moveAction)
            
            // 更新节点数组
            tileNodes[Int(oldPos.y)][Int(oldPos.x)] = nil
            tileNodes[Int(newPos.y)][Int(newPos.x)] = boxNode
            
            // 更新箱子颜色
            updateBoxAppearance(boxNode, at: newPos)
        }
    }
    
    private func updateBoxAppearance(_ boxNode: SKSpriteNode, at pos: CGPoint) {
        let tile = gameGrid[Int(pos.y)][Int(pos.x)]
        if tile == .boxOnTarget {
            boxNode.color = SKColor(red: 0.2, green: 0.8, blue: 0.2, alpha: 1.0) // 绿色表示在目标上
        } else {
            boxNode.color = SKColor(red: 0.8, green: 0.4, blue: 0.2, alpha: 1.0) // 原色
        }
    }
    
    private func checkWinCondition() {
        var allBoxesOnTargets = true
        
        for row in 0..<gridHeight {
            for col in 0..<gridWidth {
                let tile = gameGrid[row][col]
                if tile == .box {
                    allBoxesOnTargets = false
                    break
                }
            }
            if !allBoxesOnTargets { break }
        }
        
        if allBoxesOnTargets {
            print("[SimpleGameScene] 关卡完成!")
            audioManager?.playSound(.win)

            // 延迟加载下一关
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.loadNextLevel()
            }
        }
    }
    
    private func loadNextLevel() {
        let nextLevel = currentLevel + 1
        if nextLevel < 10 { // 简化版本，假设有10个关卡
            loadLevel(nextLevel)
        } else {
            print("[SimpleGameScene] 所有关卡完成!")
        }
    }
    
    // MARK: - 重置功能
    
    func resetLevel() {
        loadLevel(currentLevel)
    }
}
