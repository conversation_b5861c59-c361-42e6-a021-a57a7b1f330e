import UIKit

/// 主游戏控制器
class GameViewController: UIViewController {
    
    // MARK: - Properties
    
    /// 游戏状态管理器
    private let gameState = GameState()
    
    /// 音效管理器
    private let audioManager = SimpleAudioManager.shared
    
    /// 游戏网格集合视图
    private var collectionView: UICollectionView!
    
    /// 集合视图布局
    private var flowLayout: UICollectionViewFlowLayout!
    
    /// UI元素
    private var levelLabel: UILabel!
    private var moveCountLabel: UILabel!
    private var resetButton: UIButton!
    private var undoButton: UIButton!
    private var soundButton: UIButton!
    
    /// 单元格大小
    private var cellSize: CGFloat = 40
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupCollectionView()
        setupConstraints()
        updateUI()
        
        print("[GameViewController] 游戏控制器初始化完成")
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        calculateCellSize()
        updateCollectionViewLayout()
    }
    
    // MARK: - Setup
    
    /// 设置UI
    private func setupUI() {
        view.backgroundColor = UIColor.systemBackground

        // 隐藏导航栏以获得更沉浸的体验
        navigationController?.setNavigationBarHidden(true, animated: false)

        // 创建UI元素
        createUIElements()
        setupButtonActions()
    }
    
    /// 创建UI元素
    private func createUIElements() {
        // 关卡标签 - 更大更醒目
        levelLabel = UILabel()
        levelLabel.font = UIFont.boldSystemFont(ofSize: 24)
        levelLabel.textAlignment = .center
        levelLabel.textColor = .label
        levelLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(levelLabel)

        // 步数标签 - 改进样式
        moveCountLabel = UILabel()
        moveCountLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        moveCountLabel.textAlignment = .center
        moveCountLabel.textColor = .secondaryLabel
        moveCountLabel.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(moveCountLabel)

        // 重置按钮 - 使用图标
        resetButton = createModernButton(title: "🔄", subtitle: "重置", backgroundColor: .systemOrange)
        view.addSubview(resetButton)

        // 撤销按钮 - 使用图标
        undoButton = createModernButton(title: "↶", subtitle: "撤销", backgroundColor: .systemBlue)
        view.addSubview(undoButton)

        // 音效按钮 - 圆形设计
        soundButton = createRoundButton(title: "🔊", backgroundColor: .systemGray)
        view.addSubview(soundButton)
    }
    
    /// 创建现代化按钮（带图标和文字）
    private func createModernButton(title: String, subtitle: String, backgroundColor: UIColor) -> UIButton {
        let button = UIButton(type: .system)

        // 创建属性字符串，上面是图标，下面是文字
        let attributedTitle = NSMutableAttributedString()

        // 图标部分
        let iconAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 20),
            .foregroundColor: UIColor.white
        ]
        attributedTitle.append(NSAttributedString(string: title, attributes: iconAttributes))

        // 换行
        attributedTitle.append(NSAttributedString(string: "\n"))

        // 文字部分
        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 12, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        attributedTitle.append(NSAttributedString(string: subtitle, attributes: textAttributes))

        button.setAttributedTitle(attributedTitle, for: .normal)
        button.titleLabel?.numberOfLines = 2
        button.titleLabel?.textAlignment = .center

        button.backgroundColor = backgroundColor
        button.layer.cornerRadius = 12
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowOpacity = 0.1
        button.layer.shadowRadius = 4

        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }

    /// 创建圆形按钮
    private func createRoundButton(title: String, backgroundColor: UIColor) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 24)
        button.backgroundColor = backgroundColor
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 25 // 圆形
        button.layer.shadowColor = UIColor.black.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowOpacity = 0.1
        button.layer.shadowRadius = 4
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }

    /// 创建按钮（保留旧方法以防兼容性问题）
    private func createButton(title: String, backgroundColor: UIColor) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(title, for: .normal)
        button.backgroundColor = backgroundColor
        button.setTitleColor(.white, for: .normal)
        button.layer.cornerRadius = 8
        button.titleLabel?.font = UIFont.boldSystemFont(ofSize: 16)
        button.translatesAutoresizingMaskIntoConstraints = false
        return button
    }
    
    /// 设置按钮动作
    private func setupButtonActions() {
        resetButton.addTarget(self, action: #selector(resetButtonTapped), for: .touchUpInside)
        undoButton.addTarget(self, action: #selector(undoButtonTapped), for: .touchUpInside)
        soundButton.addTarget(self, action: #selector(soundButtonTapped), for: .touchUpInside)
    }
    
    /// 设置集合视图
    private func setupCollectionView() {
        // 创建流式布局
        flowLayout = UICollectionViewFlowLayout()
        flowLayout.minimumInteritemSpacing = 1
        flowLayout.minimumLineSpacing = 1
        flowLayout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        
        // 创建集合视图
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: flowLayout)
        collectionView.backgroundColor = UIColor.systemGray6
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.isScrollEnabled = false
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        
        // 注册单元格
        collectionView.register(
            GameCollectionViewCell.self,
            forCellWithReuseIdentifier: GameCollectionViewCell.identifier
        )
        
        view.addSubview(collectionView)
    }
    
    /// 设置约束
    private func setupConstraints() {
        let safeArea = view.safeAreaLayoutGuide

        NSLayoutConstraint.activate([
            // 关卡标签 - 顶部更多空间
            levelLabel.topAnchor.constraint(equalTo: safeArea.topAnchor, constant: 30),
            levelLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),

            // 步数标签 - 更紧凑的间距
            moveCountLabel.topAnchor.constraint(equalTo: levelLabel.bottomAnchor, constant: 8),
            moveCountLabel.centerXAnchor.constraint(equalTo: view.centerXAnchor),

            // 集合视图 - 更好的边距
            collectionView.topAnchor.constraint(equalTo: moveCountLabel.bottomAnchor, constant: 25),
            collectionView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 10),
            collectionView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -10),
            collectionView.bottomAnchor.constraint(equalTo: resetButton.topAnchor, constant: -25),

            // 按钮布局 - 更现代的设计
            resetButton.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 30),
            resetButton.bottomAnchor.constraint(equalTo: safeArea.bottomAnchor, constant: -30),
            resetButton.widthAnchor.constraint(equalToConstant: 70),
            resetButton.heightAnchor.constraint(equalToConstant: 70),

            undoButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            undoButton.bottomAnchor.constraint(equalTo: safeArea.bottomAnchor, constant: -30),
            undoButton.widthAnchor.constraint(equalToConstant: 70),
            undoButton.heightAnchor.constraint(equalToConstant: 70),

            // 音效按钮 - 圆形设计
            soundButton.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -30),
            soundButton.bottomAnchor.constraint(equalTo: safeArea.bottomAnchor, constant: -30),
            soundButton.widthAnchor.constraint(equalToConstant: 50),
            soundButton.heightAnchor.constraint(equalToConstant: 50)
        ])
    }
    
    // MARK: - Layout Calculation
    
    /// 计算单元格大小
    private func calculateCellSize() {
        let availableWidth = collectionView.bounds.width - flowLayout.sectionInset.left - flowLayout.sectionInset.right
        let availableHeight = collectionView.bounds.height - flowLayout.sectionInset.top - flowLayout.sectionInset.bottom
        
        let cols = CGFloat(gameState.cols)
        let rows = CGFloat(gameState.rows)
        
        // 计算基于宽度和高度的单元格大小
        let cellWidthBasedOnWidth = (availableWidth - (cols - 1) * flowLayout.minimumInteritemSpacing) / cols
        let cellHeightBasedOnHeight = (availableHeight - (rows - 1) * flowLayout.minimumLineSpacing) / rows
        
        // 选择较小的值以确保网格完全可见
        cellSize = min(cellWidthBasedOnWidth, cellHeightBasedOnHeight, 60) // 最大60点
        cellSize = max(cellSize, 20) // 最小20点
    }
    
    /// 更新集合视图布局
    private func updateCollectionViewLayout() {
        flowLayout.itemSize = CGSize(width: cellSize, height: cellSize)
        collectionView.collectionViewLayout.invalidateLayout()
    }
    
    // MARK: - UI Updates
    
    /// 更新UI
    private func updateUI() {
        levelLabel.text = "关卡 \(gameState.currentLevel + 1)"
        moveCountLabel.text = "步数: \(gameState.moveCount)"
        soundButton.setTitle(audioManager.isSoundOn() ? "🔊" : "🔇", for: .normal)
        
        collectionView.reloadData()
    }
    
    // MARK: - Button Actions
    
    @objc private func resetButtonTapped() {
        audioManager.playButtonSound()
        gameState.resetLevel()
        updateUI()
        
        showMessage("关卡已重置")
    }
    
    @objc private func undoButtonTapped() {
        audioManager.playButtonSound()
        
        if gameState.undoLastMove() {
            updateUI()
            showMessage("已撤销上一步")
        } else {
            audioManager.playErrorSound()
            showMessage("无法撤销")
        }
    }
    
    @objc private func soundButtonTapped() {
        audioManager.toggleSound()
        updateUI()
    }
    
    // MARK: - Game Logic
    
    /// 处理玩家移动
    private func handlePlayerMove(to indexPath: IndexPath) {
        let targetPosition = indexPathToGridPosition(indexPath)
        
        if gameState.attemptMove(to: targetPosition) {
            // 移动成功
            let targetTile = gameState.getTileType(at: targetPosition)
            
            if targetTile.isBox {
                audioManager.playPushSound()
            } else {
                audioManager.playMoveSound()
            }
            
            updateUI()

            // 检查胜利条件
            if gameState.checkWinCondition() {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    self.handleLevelComplete()
                }
            } else {
                // 检查死锁状态
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    self.checkForDeadlock()
                }
            }
        } else {
            // 移动失败
            audioManager.playErrorSound()
        }
    }
    
    /// 处理关卡完成
    private func handleLevelComplete() {
        audioManager.playWinSound()
        
        let alert = UIAlertController(
            title: "恭喜！",
            message: "关卡完成！\n步数: \(gameState.moveCount)",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "下一关", style: .default) { _ in
            self.gameState.nextLevel()
            self.updateUI()
        })
        
        alert.addAction(UIAlertAction(title: "重玩", style: .default) { _ in
            self.gameState.resetLevel()
            self.updateUI()
        })
        
        present(alert, animated: true)
    }

    /// 检查死锁状态
    private func checkForDeadlock() {
        if gameState.isDeadlocked() {
            audioManager.playErrorSound()

            let alert = UIAlertController(
                title: "无解状态",
                message: "此关卡无法完成。\n原因：\(gameState.getDeadlockDescription())\n\n是否重置关卡？",
                preferredStyle: .alert
            )

            alert.addAction(UIAlertAction(title: "重置", style: .default) { _ in
                self.gameState.resetLevel()
                self.updateUI()
                self.showMessage("关卡已重置")
            })

            alert.addAction(UIAlertAction(title: "撤销", style: .default) { _ in
                if self.gameState.undoLastMove() {
                    self.updateUI()
                    self.showMessage("已撤销上一步")
                } else {
                    // 如果无法撤销，强制重置
                    self.gameState.resetLevel()
                    self.updateUI()
                    self.showMessage("无法撤销，关卡已重置")
                }
            })

            present(alert, animated: true)
        }
    }

    // MARK: - Helper Methods

    /// 显示消息提示
    private func showMessage(_ message: String) {
        let alert = UIAlertController(title: nil, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    /// 将IndexPath转换为网格位置
    private func indexPathToGridPosition(_ indexPath: IndexPath) -> GridPosition {
        let row = indexPath.item / gameState.cols
        let col = indexPath.item % gameState.cols
        return GridPosition(row: row, col: col)
    }
    
    /// 将网格位置转换为IndexPath
    private func gridPositionToIndexPath(_ position: GridPosition) -> IndexPath {
        let item = position.row * gameState.cols + position.col
        return IndexPath(item: item, section: 0)
    }
    
    /// 显示消息
    private func showMessage(_ message: String) {
        // 简单的消息显示（可以后续改为Toast样式）
        print("[GameViewController] \(message)")
    }
}

// MARK: - UICollectionViewDataSource

extension GameViewController: UICollectionViewDataSource {

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return gameState.rows * gameState.cols
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(
            withReuseIdentifier: GameCollectionViewCell.identifier,
            for: indexPath
        ) as! GameCollectionViewCell

        let gridPosition = indexPathToGridPosition(indexPath)
        let tileType = gameState.getTileType(at: gridPosition)

        cell.configure(with: tileType)

        return cell
    }
}

// MARK: - UICollectionViewDelegate

extension GameViewController: UICollectionViewDelegate {

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)

        let targetPosition = indexPathToGridPosition(indexPath)
        let currentPlayerPosition = gameState.playerPosition

        // 检查是否是相邻位置
        let rowDiff = abs(targetPosition.row - currentPlayerPosition.row)
        let colDiff = abs(targetPosition.col - currentPlayerPosition.col)

        // 只允许移动到相邻位置
        if rowDiff + colDiff == 1 {
            handlePlayerMove(to: indexPath)
        } else {
            audioManager.playErrorSound()
            showMessage("只能移动到相邻位置")
        }
    }
}

// MARK: - UICollectionViewDelegateFlowLayout

extension GameViewController: UICollectionViewDelegateFlowLayout {

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: cellSize, height: cellSize)
    }
}
