import UIKit

/// 游戏网格单元格
class GameCollectionViewCell: UICollectionViewCell {
    
    // MARK: - Properties
    
    static let identifier = "GameCollectionViewCell"
    
    /// 自定义背景视图
    private let customBackgroundView = UIView()
    
    /// 内容标签
    private let contentLabel = UILabel()
    
    /// 当前瓦片类型
    private var currentTileType: TileType = .empty
    
    // MARK: - Initialization
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    
    private func setupUI() {
        // 设置背景视图
        customBackgroundView.layer.cornerRadius = 4
        customBackgroundView.layer.borderWidth = 0.5
        customBackgroundView.layer.borderColor = UIColor.lightGray.cgColor
        contentView.addSubview(customBackgroundView)
        
        // 设置内容标签
        contentLabel.textAlignment = .center
        contentLabel.font = UIFont.systemFont(ofSize: 20)
        contentLabel.adjustsFontSizeToFitWidth = true
        contentLabel.minimumScaleFactor = 0.5
        contentView.addSubview(contentLabel)
        
        // 设置约束
        customBackgroundView.translatesAutoresizingMaskIntoConstraints = false
        contentLabel.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            // 背景视图约束
            customBackgroundView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 1),
            customBackgroundView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 1),
            customBackgroundView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -1),
            customBackgroundView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -1),

            // 内容标签约束
            contentLabel.centerXAnchor.constraint(equalTo: customBackgroundView.centerXAnchor),
            contentLabel.centerYAnchor.constraint(equalTo: customBackgroundView.centerYAnchor),
            contentLabel.widthAnchor.constraint(lessThanOrEqualTo: customBackgroundView.widthAnchor, constant: -4),
            contentLabel.heightAnchor.constraint(lessThanOrEqualTo: customBackgroundView.heightAnchor, constant: -4)
        ])
    }
    
    // MARK: - Configuration
    
    /// 配置单元格
    func configure(with tileType: TileType) {
        currentTileType = tileType

        // 设置背景颜色
        customBackgroundView.backgroundColor = tileType.color

        // 设置显示文本
        contentLabel.text = tileType.displayText

        // 根据瓦片类型调整样式
        switch tileType {
        case .wall:
            customBackgroundView.layer.borderWidth = 1
            customBackgroundView.layer.borderColor = UIColor.darkGray.cgColor
            addShadowEffect()

        case .target:
            customBackgroundView.layer.borderWidth = 2
            customBackgroundView.layer.borderColor = UIColor.systemGreen.cgColor
            addPulseAnimation()

        case .boxOnTarget:
            customBackgroundView.layer.borderWidth = 2
            customBackgroundView.layer.borderColor = UIColor.systemGreen.cgColor
            addSuccessEffect()

        case .player, .playerOnTarget:
            addBounceEffect()

        default:
            removePreviousEffects()
        }
    }
    
    // MARK: - Animation Effects
    
    /// 添加阴影效果（墙壁）
    private func addShadowEffect() {
        customBackgroundView.layer.shadowColor = UIColor.black.cgColor
        customBackgroundView.layer.shadowOffset = CGSize(width: 2, height: 2)
        customBackgroundView.layer.shadowOpacity = 0.3
        customBackgroundView.layer.shadowRadius = 2
    }

    /// 添加脉冲动画（目标点）
    private func addPulseAnimation() {
        let pulseAnimation = CABasicAnimation(keyPath: "transform.scale")
        pulseAnimation.duration = 1.0
        pulseAnimation.fromValue = 1.0
        pulseAnimation.toValue = 1.1
        pulseAnimation.autoreverses = true
        pulseAnimation.repeatCount = .infinity
        customBackgroundView.layer.add(pulseAnimation, forKey: "pulse")
    }

    /// 添加成功效果（箱子在目标点上）
    private func addSuccessEffect() {
        let glowAnimation = CABasicAnimation(keyPath: "shadowOpacity")
        glowAnimation.duration = 0.5
        glowAnimation.fromValue = 0.0
        glowAnimation.toValue = 0.8
        glowAnimation.autoreverses = true
        glowAnimation.repeatCount = .infinity

        customBackgroundView.layer.shadowColor = UIColor.systemGreen.cgColor
        customBackgroundView.layer.shadowRadius = 4
        customBackgroundView.layer.add(glowAnimation, forKey: "glow")
    }
    
    /// 添加弹跳效果（玩家）
    private func addBounceEffect() {
        let bounceAnimation = CAKeyframeAnimation(keyPath: "transform.scale")
        bounceAnimation.values = [1.0, 1.2, 1.0]
        bounceAnimation.duration = 0.3
        bounceAnimation.timingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
        contentLabel.layer.add(bounceAnimation, forKey: "bounce")
    }
    
    /// 移除之前的效果
    private func removePreviousEffects() {
        customBackgroundView.layer.removeAllAnimations()
        customBackgroundView.layer.shadowOpacity = 0
        customBackgroundView.layer.borderWidth = 0.5
        customBackgroundView.layer.borderColor = UIColor.lightGray.cgColor
    }
    
    // MARK: - Move Animation
    
    /// 执行移动动画
    func animateMove(to newPosition: CGPoint, completion: @escaping () -> Void) {
        UIView.animate(
            withDuration: 0.2,
            delay: 0,
            usingSpringWithDamping: 0.8,
            initialSpringVelocity: 0.5,
            options: [.curveEaseInOut],
            animations: {
                self.center = newPosition
            },
            completion: { _ in
                completion()
            }
        )
    }
    
    /// 执行推箱子动画
    func animatePush(completion: @escaping () -> Void) {
        // 先缩小
        UIView.animate(
            withDuration: 0.1,
            animations: {
                self.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            },
            completion: { _ in
                // 再恢复
                UIView.animate(
                    withDuration: 0.1,
                    animations: {
                        self.transform = .identity
                    },
                    completion: { _ in
                        completion()
                    }
                )
            }
        )
    }
    
    // MARK: - Reuse
    
    override func prepareForReuse() {
        super.prepareForReuse()
        removePreviousEffects()
        contentLabel.text = ""
        customBackgroundView.backgroundColor = .clear
        transform = .identity
    }
}
