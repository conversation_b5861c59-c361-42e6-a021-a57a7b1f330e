//
//  LevelSelectionManager.swift
//  SuperSokoban
//
//  Created by AI Assistant on 2024.
//  Copyright © 2024 SuperSokoban. All rights reserved.
//

import Foundation
import SpriteKit

/// 关卡选择管理器 - 负责关卡选择界面的创建和管理
class LevelSelectionManager {
    
    // MARK: - Properties
    private weak var gameScene: GameScene?
    private var isMenuVisible = false
    
    // MARK: - Initialization
    init(gameScene: GameScene) {
        self.gameScene = gameScene
    }
    
    // MARK: - Public Methods
    
    /// 显示关卡选择菜单
    func showLevelSelectionMenu() {
        guard let scene = gameScene, !isMenuVisible else { return }
        
        print("[LevelSelectionManager] 显示关卡选择菜单")
        isMenuVisible = true
        
        // 创建菜单组件
        createMenuOverlay()
        createMenuPanel()
        createMenuTitle()
        createLevelButtons()
        createCloseButton()
        
        // 播放入场动画
        playMenuEntranceAnimation()
    }
    
    /// 关闭关卡选择菜单
    func closeLevelSelectionMenu() {
        guard let scene = gameScene, isMenuVisible else { return }
        
        print("[LevelSelectionManager] 关闭关卡选择菜单")
        isMenuVisible = false
        
        // 移除所有关卡选择相关的节点
        scene.children.filter { node in
            node.name?.contains("levelSelect") == true ||
            node.name?.hasPrefix("levelButton_") == true ||
            node.name?.hasPrefix("levelNumber_") == true ||
            node.name?.hasPrefix("levelStar_") == true ||
            node.name?.hasPrefix("tutorialMark_") == true ||
            node.name?.hasPrefix("lockedLevel_") == true ||
            node.name?.hasPrefix("lockedNumber_") == true
        }.forEach { $0.removeFromParent() }
    }
    
    // MARK: - Private Methods
    
    /// 创建菜单背景遮罩
    private func createMenuOverlay() {
        guard let scene = gameScene else { return }
        
        let overlay = SKSpriteNode(color: SKColor.black.withAlphaComponent(0.7), size: scene.size)
        overlay.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        overlay.zPosition = 200
        overlay.name = "levelSelectOverlay"
        scene.addChild(overlay)
    }
    
    /// 创建菜单面板
    private func createMenuPanel() {
        guard let scene = gameScene else { return }
        
        let menuPanel = SKShapeNode(rectOf: CGSize(width: 400, height: 500), cornerRadius: 20)
        menuPanel.fillColor = SKColor.white.withAlphaComponent(0.95)
        menuPanel.strokeColor = SKColor.systemPurple
        menuPanel.lineWidth = 4
        menuPanel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2)
        menuPanel.zPosition = 201
        menuPanel.name = "levelSelectPanel"
        scene.addChild(menuPanel)
    }
    
    /// 创建菜单标题
    private func createMenuTitle() {
        guard let scene = gameScene else { return }
        
        let titleLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        titleLabel.text = "选择关卡"
        titleLabel.fontSize = 28
        titleLabel.fontColor = SKColor.systemPurple
        titleLabel.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 + 200)
        titleLabel.zPosition = 202
        titleLabel.name = "levelSelectTitle"
        scene.addChild(titleLabel)
    }
    
    /// 创建关卡按钮网格
    private func createLevelButtons() {
        guard let scene = gameScene else { return }
        
        // 获取总关卡数 - 全部使用动态生成关卡
        let totalLevels = min(scene.levelGenerator.numberOfLevels, 50) // 显示50个动态关卡
        let levelsPerRow = 5
        let buttonSize: CGFloat = 60
        let spacing: CGFloat = 80
        
        // 创建关卡按钮网格
        for i in 0..<totalLevels {
            let row = i / levelsPerRow
            let col = i % levelsPerRow
            
            let buttonX = scene.size.width/2 - CGFloat(levelsPerRow - 1) * spacing / 2 + CGFloat(col) * spacing
            let buttonY = scene.size.height/2 + 120 - CGFloat(row) * spacing
            
            createSingleLevelButton(
                levelIndex: i,
                position: CGPoint(x: buttonX, y: buttonY),
                buttonSize: buttonSize
            )
        }
    }
    
    /// 创建单个关卡按钮
    private func createSingleLevelButton(levelIndex: Int, position: CGPoint, buttonSize: CGFloat) {
        guard let scene = gameScene else { return }

        // 检查关卡状态
        let isCompleted = scene.progressManager.isLevelCompleted(levelIndex: levelIndex)
        let isUnlocked = scene.progressManager.isLevelUnlocked(levelIndex: levelIndex)
        let isCurrent = levelIndex == scene.currentLevelIndex

        // 创建按钮背景
        let buttonBackground = createButtonBackground(
            isCompleted: isCompleted,
            isUnlocked: isUnlocked,
            isCurrent: isCurrent,
            position: position,
            buttonSize: buttonSize,
            levelIndex: levelIndex
        )
        scene.addChild(buttonBackground)

        // 创建关卡数字
        let levelNumber = createLevelNumber(
            levelIndex: levelIndex,
            isUnlocked: isUnlocked,
            position: position
        )
        scene.addChild(levelNumber)

        // 添加特殊标记
        if isCompleted {
            let star = createCompletionStar(levelIndex: levelIndex, position: position)
            scene.addChild(star)
        }

        // 删除教学关卡标记，因为现在所有关卡都是动态生成的
    }
    
    /// 创建按钮背景
    private func createButtonBackground(isCompleted: Bool, isUnlocked: Bool, isCurrent: Bool, position: CGPoint, buttonSize: CGFloat, levelIndex: Int) -> SKShapeNode {
        let buttonBackground = SKShapeNode(rectOf: CGSize(width: buttonSize, height: buttonSize), cornerRadius: 10)
        
        if isCurrent {
            buttonBackground.fillColor = SKColor.systemBlue // 当前关卡高亮
            buttonBackground.strokeColor = SKColor.white
        } else if isCompleted {
            buttonBackground.fillColor = SKColor.systemGreen // 已完成关卡
            buttonBackground.strokeColor = SKColor.white
        } else if isUnlocked {
            buttonBackground.fillColor = SKColor.systemOrange // 可游玩关卡
            buttonBackground.strokeColor = SKColor.white
        } else {
            buttonBackground.fillColor = SKColor.systemGray // 锁定关卡
            buttonBackground.strokeColor = SKColor.darkGray
        }
        
        buttonBackground.lineWidth = 2
        buttonBackground.position = position
        buttonBackground.zPosition = 202
        buttonBackground.name = isUnlocked ? "levelButton_\(levelIndex)" : "lockedLevel_\(levelIndex)"
        
        return buttonBackground
    }
    
    /// 创建关卡数字标签
    private func createLevelNumber(levelIndex: Int, isUnlocked: Bool, position: CGPoint) -> SKLabelNode {
        let levelNumber = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        levelNumber.text = "\(levelIndex + 1)"
        levelNumber.fontSize = 20
        levelNumber.fontColor = isUnlocked ? SKColor.white : SKColor.gray
        levelNumber.horizontalAlignmentMode = .center
        levelNumber.verticalAlignmentMode = .center
        levelNumber.position = position
        levelNumber.zPosition = 203
        levelNumber.name = isUnlocked ? "levelNumber_\(levelIndex)" : "lockedNumber_\(levelIndex)"
        
        return levelNumber
    }
    
    /// 创建完成星星标记
    private func createCompletionStar(levelIndex: Int, position: CGPoint) -> SKLabelNode {
        let star = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        star.text = "⭐"
        star.fontSize = 16
        star.position = CGPoint(x: position.x + 15, y: position.y + 15)
        star.zPosition = 204
        star.name = "levelStar_\(levelIndex)"
        
        return star
    }
    
    // 教学关卡标记已删除，因为现在所有关卡都是动态生成的
    
    /// 创建关闭按钮
    private func createCloseButton() {
        guard let scene = gameScene else { return }
        
        let closeButton = SKShapeNode(rectOf: CGSize(width: 100, height: 40), cornerRadius: 20)
        closeButton.fillColor = SKColor.systemRed
        closeButton.strokeColor = SKColor.white
        closeButton.lineWidth = 2
        closeButton.position = CGPoint(x: scene.size.width/2, y: scene.size.height/2 - 200)
        closeButton.zPosition = 202
        closeButton.name = "levelSelectClose"
        scene.addChild(closeButton)
        
        let closeLabel = SKLabelNode(fontNamed: "HelveticaNeue-Bold")
        closeLabel.text = "关闭"
        closeLabel.fontSize = 18
        closeLabel.fontColor = SKColor.white
        closeLabel.horizontalAlignmentMode = .center
        closeLabel.verticalAlignmentMode = .center
        closeLabel.position = closeButton.position
        closeLabel.zPosition = 203
        closeLabel.name = "levelSelectCloseLabel"
        scene.addChild(closeLabel)
    }
    
    /// 播放菜单入场动画
    private func playMenuEntranceAnimation() {
        guard let scene = gameScene else { return }
        
        if let menuPanel = scene.childNode(withName: "levelSelectPanel") {
            menuPanel.setScale(0)
            let scaleUp = SKAction.scale(to: 1.0, duration: 0.3)
            scaleUp.timingMode = .easeOut
            menuPanel.run(scaleUp)
        }
    }
}
