import Foundation

/// 死锁检测器 - 检测游戏中的无解状态
class DeadlockDetector {
    
    // MARK: - Properties
    
    /// 游戏状态引用
    private weak var gameState: GameState?
    
    // MARK: - Initialization
    
    init(gameState: GameState) {
        self.gameState = gameState
    }
    
    // MARK: - Public Methods
    
    /// 检测当前状态是否为死锁
    func isDeadlocked() -> <PERSON><PERSON> {
        guard let gameState = gameState else { return false }
        
        // 检查各种死锁类型
        return hasCornerDeadlock() || 
               hasWallDeadlock() || 
               hasBoxGroupDeadlock() ||
               hasInaccessibleTargets()
    }
    
    /// 获取死锁描述
    func getDeadlockDescription() -> String {
        if hasCornerDeadlock() {
            return "箱子被推到角落，无法移动"
        } else if hasWallDeadlock() {
            return "箱子靠墙且无可达目标"
        } else if hasBoxGroupDeadlock() {
            return "多个箱子形成无法移动的组合"
        } else if hasInaccessibleTargets() {
            return "目标点无法到达"
        } else {
            return "检测到无解状态"
        }
    }
    
    // MARK: - Deadlock Detection Methods
    
    /// 检测角落死锁
    private func hasCornerDeadlock() -> <PERSON><PERSON> {
        guard let gameState = gameState else { return false }
        
        for row in 0..<gameState.rows {
            for col in 0..<gameState.cols {
                let position = GridPosition(row: row, col: col)
                let tile = gameState.getTileType(at: position)
                
                // 只检查不在目标点上的箱子
                if tile == .box && isInCorner(position) {
                    return true
                }
            }
        }
        
        return false
    }
    
    /// 检测墙边死锁
    private func hasWallDeadlock() -> Bool {
        guard let gameState = gameState else { return false }
        
        for row in 0..<gameState.rows {
            for col in 0..<gameState.cols {
                let position = GridPosition(row: row, col: col)
                let tile = gameState.getTileType(at: position)
                
                // 只检查不在目标点上的箱子
                if tile == .box && isAgainstWallWithoutTarget(position) {
                    return true
                }
            }
        }
        
        return false
    }
    
    /// 检测箱子组合死锁
    private func hasBoxGroupDeadlock() -> Bool {
        guard let gameState = gameState else { return false }
        
        // 检查2x2的箱子组合
        for row in 0..<gameState.rows-1 {
            for col in 0..<gameState.cols-1 {
                if isDeadlockedBoxGroup(topLeft: GridPosition(row: row, col: col)) {
                    return true
                }
            }
        }
        
        return false
    }
    
    /// 检测无法到达的目标点
    private func hasInaccessibleTargets() -> Bool {
        guard let gameState = gameState else { return false }
        
        // 获取所有目标点和箱子位置
        var targets: [GridPosition] = []
        var boxes: [GridPosition] = []
        
        for row in 0..<gameState.rows {
            for col in 0..<gameState.cols {
                let position = GridPosition(row: row, col: col)
                let tile = gameState.getTileType(at: position)
                
                if tile == .target {
                    targets.append(position)
                } else if tile == .box {
                    boxes.append(position)
                }
            }
        }
        
        // 检查是否有足够的可达目标点
        return !canAllBoxesReachTargets(boxes: boxes, targets: targets)
    }
    
    // MARK: - Helper Methods
    
    /// 检查位置是否在角落
    private func isInCorner(_ position: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        
        let adjacentWalls = getAdjacentWalls(position)
        
        // 如果有两个相邻的墙壁，就是角落
        return adjacentWalls.count >= 2 && areWallsAdjacent(adjacentWalls)
    }
    
    /// 检查箱子是否靠墙且没有目标点
    private func isAgainstWallWithoutTarget(_ position: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        
        let adjacentWalls = getAdjacentWalls(position)
        
        // 如果靠墙，检查是否有可达的目标点
        if adjacentWalls.count > 0 {
            return !hasReachableTarget(from: position)
        }
        
        return false
    }
    
    /// 获取相邻的墙壁方向
    private func getAdjacentWalls(_ position: GridPosition) -> [Direction] {
        guard let gameState = gameState else { return [] }
        
        var walls: [Direction] = []
        let directions: [Direction] = [.up, .down, .left, .right]
        
        for direction in directions {
            let adjacentPos = position.moved(by: direction)
            if !isValidPosition(adjacentPos) || gameState.getTileType(at: adjacentPos) == .wall {
                walls.append(direction)
            }
        }
        
        return walls
    }
    
    /// 检查墙壁是否相邻（形成角落）
    private func areWallsAdjacent(_ walls: [Direction]) -> Bool {
        let adjacentPairs: [(Direction, Direction)] = [
            (.up, .left), (.up, .right),
            (.down, .left), (.down, .right)
        ]
        
        for (dir1, dir2) in adjacentPairs {
            if walls.contains(dir1) && walls.contains(dir2) {
                return true
            }
        }
        
        return false
    }
    
    /// 检查是否是死锁的箱子组合
    private func isDeadlockedBoxGroup(topLeft: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        
        let positions = [
            topLeft,
            GridPosition(row: topLeft.row, col: topLeft.col + 1),
            GridPosition(row: topLeft.row + 1, col: topLeft.col),
            GridPosition(row: topLeft.row + 1, col: topLeft.col + 1)
        ]
        
        // 检查是否所有位置都有箱子（不在目标点上）
        var boxCount = 0
        for position in positions {
            if isValidPosition(position) && gameState.getTileType(at: position) == .box {
                boxCount += 1
            }
        }
        
        // 如果有3个或4个箱子形成方块，可能是死锁
        return boxCount >= 3
    }
    
    /// 检查箱子是否有可达的目标点
    private func hasReachableTarget(from position: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        
        // 简化版本：检查是否有目标点在同一行或列
        for row in 0..<gameState.rows {
            for col in 0..<gameState.cols {
                let targetPos = GridPosition(row: row, col: col)
                let tile = gameState.getTileType(at: targetPos)
                
                if tile == .target {
                    // 检查是否在同一行或列，且路径可能通畅
                    if (targetPos.row == position.row || targetPos.col == position.col) {
                        return true
                    }
                }
            }
        }
        
        return false
    }
    
    /// 检查所有箱子是否能到达目标点
    private func canAllBoxesReachTargets(boxes: [GridPosition], targets: [GridPosition]) -> Bool {
        // 简化版本：检查箱子数量是否等于目标点数量
        return boxes.count <= targets.count
    }
    
    /// 检查位置是否有效
    private func isValidPosition(_ position: GridPosition) -> Bool {
        guard let gameState = gameState else { return false }
        return position.row >= 0 && position.row < gameState.rows &&
               position.col >= 0 && position.col < gameState.cols
    }
}

/// 方向枚举扩展
extension Direction {
    /// 获取所有方向
    static let allDirections: [Direction] = [.up, .down, .left, .right]
}
