import AVFoundation
import UIKit

/// 简化的音效管理器
class SimpleAudioManager {
    
    // MARK: - Properties
    
    static let shared = SimpleAudioManager()
    
    /// 音效播放器
    private var audioPlayers: [String: AVAudioPlayer] = [:]
    
    /// 是否启用音效
    private var isSoundEnabled: Bool = true
    
    // MARK: - Sound Types
    
    enum SoundType: String, CaseIterable {
        case move = "move"
        case push = "push"
        case win = "win"
        case error = "error"
        case button = "button"
        
        /// 获取音效文件名
        var fileName: String {
            switch self {
            case .move:
                return "move_sound"
            case .push:
                return "push_sound"
            case .win:
                return "win_sound"
            case .error:
                return "error_sound"
            case .button:
                return "button_sound"
            }
        }
        
        /// 获取音效音量
        var volume: Float {
            switch self {
            case .move:
                return 0.3
            case .push:
                return 0.5
            case .win:
                return 0.8
            case .error:
                return 0.4
            case .button:
                return 0.2
            }
        }
    }
    
    // MARK: - Initialization
    
    private init() {
        setupAudioSession()
        preloadSounds()
    }
    
    // MARK: - Setup
    
    /// 设置音频会话
    private func setupAudioSession() {
        do {
            try AVAudioSession.sharedInstance().setCategory(.ambient, mode: .default)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("[SimpleAudioManager] 音频会话设置失败: \(error)")
        }
    }
    
    /// 预加载音效
    private func preloadSounds() {
        for soundType in SoundType.allCases {
            loadSound(soundType)
        }
    }
    
    /// 加载单个音效
    private func loadSound(_ soundType: SoundType) {
        // 由于我们没有实际的音效文件，这里使用系统音效作为替代
        // 在实际项目中，你应该添加真实的音效文件到Bundle中
        
        // 创建一个简单的音调作为音效
        createSystemSound(for: soundType)
    }
    
    /// 创建系统音效
    private func createSystemSound(for soundType: SoundType) {
        // 使用不同的系统音效ID来模拟不同的游戏音效
        let systemSoundID: SystemSoundID
        
        switch soundType {
        case .move:
            systemSoundID = 1104 // 短促的点击音
        case .push:
            systemSoundID = 1105 // 较重的点击音
        case .win:
            systemSoundID = 1107 // 成功音
        case .error:
            systemSoundID = 1006 // 错误音
        case .button:
            systemSoundID = 1104 // 按钮点击音
        }
        
        // 注册系统音效（这里只是示例，实际使用时应该加载音频文件）
        print("[SimpleAudioManager] 注册音效: \(soundType.rawValue) -> SystemSound \(systemSoundID)")
    }
    
    // MARK: - Public Methods
    
    /// 播放音效
    func playSound(_ soundType: SoundType) {
        guard isSoundEnabled else { return }
        
        // 使用系统音效播放（临时方案）
        playSystemSound(for: soundType)
        
        // 添加触觉反馈
        addHapticFeedback(for: soundType)
    }
    
    /// 播放系统音效
    private func playSystemSound(for soundType: SoundType) {
        let systemSoundID: SystemSoundID
        
        switch soundType {
        case .move:
            systemSoundID = 1104
        case .push:
            systemSoundID = 1105
        case .win:
            systemSoundID = 1107
        case .error:
            systemSoundID = 1006
        case .button:
            systemSoundID = 1104
        }
        
        AudioServicesPlaySystemSound(systemSoundID)
    }
    
    /// 添加触觉反馈
    private func addHapticFeedback(for soundType: SoundType) {
        let impactFeedback: UIImpactFeedbackGenerator
        
        switch soundType {
        case .move:
            impactFeedback = UIImpactFeedbackGenerator(style: .light)
        case .push:
            impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        case .win:
            impactFeedback = UIImpactFeedbackGenerator(style: .heavy)
        case .error:
            impactFeedback = UIImpactFeedbackGenerator(style: .rigid)
        case .button:
            impactFeedback = UIImpactFeedbackGenerator(style: .light)
        }
        
        impactFeedback.impactOccurred()
    }
    
    /// 切换音效开关
    func toggleSound() {
        isSoundEnabled.toggle()
        
        if isSoundEnabled {
            playSound(.button)
        }
        
        print("[SimpleAudioManager] 音效\(isSoundEnabled ? "开启" : "关闭")")
    }
    
    /// 设置音效开关
    func setSoundEnabled(_ enabled: Bool) {
        isSoundEnabled = enabled
    }
    
    /// 获取音效状态
    func isSoundOn() -> Bool {
        return isSoundEnabled
    }
    
    /// 播放移动音效
    func playMoveSound() {
        playSound(.move)
    }
    
    /// 播放推箱子音效
    func playPushSound() {
        playSound(.push)
    }
    
    /// 播放胜利音效
    func playWinSound() {
        playSound(.win)
    }
    
    /// 播放错误音效
    func playErrorSound() {
        playSound(.error)
    }
    
    /// 播放按钮音效
    func playButtonSound() {
        playSound(.button)
    }
    
    // MARK: - Cleanup
    
    /// 清理资源
    func cleanup() {
        audioPlayers.removeAll()
    }
}
